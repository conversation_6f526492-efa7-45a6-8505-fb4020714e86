"use client"

import { useState, useEffect } from "react"
import { Bug, X, Monitor, Globe, ArrowDown, CheckCircle,  MapPin, Zap, Shield, Eye, Settings, Smartphone, Wrench} from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useForm, ValidationError } from '@formspree/react'


export default function FloatingBugReport() {
  const [isOpen, setIsOpen] = useState(false)
  const [showWelcome, setShowWelcome] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [priority, setPriority] = useState("medium")
  const [category, setCategory] = useState("ui")
  const [state, handleSubmit] = useForm(process.env.NEXT_PUBLIC_FORMSPREE_ID || "xkgzqled")



  // Show welcome dialog near button
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('bug-report-seen')
    if (!hasSeenWelcome) {
      const timer = setTimeout(() => {
        setShowWelcome(true)
        // Auto-hide after 6 seconds
        setTimeout(() => {
          setShowWelcome(false)
          localStorage.setItem('bug-report-seen', 'true')
        }, 6000)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [])

  const getBrowserInfo = () => {
    const ua = navigator.userAgent
    let browser = "Unknown"
    if (ua.includes("Chrome")) browser = "Chrome"
    else if (ua.includes("Firefox")) browser = "Firefox"
    else if (ua.includes("Safari")) browser = "Safari"
    else if (ua.includes("Edge")) browser = "Edge"
    return `${browser} - ${getDeviceType()}`
  }

  const getDeviceType = () => {
    const ua = navigator.userAgent
    if (/tablet|ipad|playbook|silk/i.test(ua)) return "Tablet"
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(ua)) return "Mobile"
    return "Desktop"
  }

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    console.log('Form submission started...')
    console.log('Formspree ID:', process.env.NEXT_PUBLIC_FORMSPREE_ID || "xkgzqled")

    // Let Formspree handle the submission
    await handleSubmit(e)

    console.log('Form submission completed. State:', state)
  }

  // Watch for successful submission
  useEffect(() => {
    if (state.succeeded) {
      setIsOpen(false)
      setShowSuccess(true)
    }
  }, [state.succeeded])

  // Success dialog
  const SuccessDialog = () => (
    <div className="fixed bottom-6 left-6 z-50">
      <Card className="w-72 border-green-500 bg-green-50 dark:bg-green-950 shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
            <CheckCircle className="w-5 h-5" />
            <p className="font-medium">Bug report submitted!</p>
          </div>
          <p className="text-sm text-green-600 dark:text-green-400 mt-1">
            Thanks for helping us improve.
          </p>
          <Button
            variant="ghost"
            size="sm"
            className="mt-2 h-7 px-2 text-xs"
            onClick={() => {
              // Just hide the success dialog, no reload
              setShowSuccess(false)
              setPriority("medium")
              setCategory("ui")
            }}
          >
            Close
          </Button>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <>
      {/* Success Dialog */}
      {showSuccess && <SuccessDialog />}

      {/* Welcome Dialog - positioned near button */}
      {showWelcome && (
        <div className="fixed bottom-20 left-6 z-40">
          <Card className="w-64 shadow-xl border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
            <CardContent className="p-4">
              <div className="flex items-start gap-2">
                <Bug className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
                <div>
                  <p className="font-medium text-sm text-orange-800 dark:text-orange-200">
                    Found a bug?
                  </p>
                  <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
                    Click the button below to report issues quickly
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-orange-600 hover:text-orange-800"
                  onClick={() => {
                    setShowWelcome(false)
                    localStorage.setItem('bug-report-seen', 'true')
                  }}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
              <div className="flex justify-center mt-2">
                <ArrowDown className="w-4 h-4 text-orange-600 dark:text-orange-400 animate-bounce" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Floating Bug Report Button */}
      <div className="fixed bottom-10 left-4 md:left-6 lg:left-10 z-[9999]">
        <Button
          onClick={() => setIsOpen(true)}
          size="sm"
          className="h-11 w-11 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 bg-orange-600 hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600"
          aria-label="Report a bug"
        >
          <Bug className="w-5 h-5" />
        </Button>
      </div>

      {/* Bug Report Modal */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-[9998] backdrop-blur-sm"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setIsOpen(false)
            }
          }}
        >
          <Card className="w-full max-w-2xl max-h-[85vh] flex flex-col shadow-2xl border-0">
            <div className="overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-200 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:border-2 [&::-webkit-scrollbar-thumb]:border-transparent [&::-webkit-scrollbar-thumb]:bg-clip-padding dark:[&::-webkit-scrollbar-thumb]:bg-gray-700 hover:[&::-webkit-scrollbar-thumb]:bg-gray-300 dark:hover:[&::-webkit-scrollbar-thumb]:bg-gray-600 [&::-webkit-scrollbar-thumb]:transition-colors [&::-webkit-scrollbar-thumb]:duration-200">
              <CardHeader className="relative pb-6 px-8 pt-6 flex-shrink-0 border-b">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
                  onClick={() => setIsOpen(false)}
                  aria-label="Close bug report form"
                >
                  <X className="w-5 h-5" />
                </Button>

                <CardTitle className="flex items-center gap-3 pr-12 text-xl">
                  <Bug className="w-6 h-6 text-orange-500" />
                  Report a Bug
                </CardTitle>
                <CardDescription className="text-base mt-2 text-muted-foreground">
                  Help us fix issues quickly by providing key details
                </CardDescription>
              </CardHeader>

              <CardContent className="px-8 py-6">
                <form onSubmit={onSubmit} className="space-y-6">
                  {/* Honeypot field for spam protection */}
                  <input type="text" name="_gotcha" style={{ display: 'none' }} tabIndex={-1} autoComplete="off" />

                  {/* Form values for Select components */}
                  <input type="hidden" name="priority" value={priority} />
                  <input type="hidden" name="category" value={category} />

                  {/* Essential technical fields only */}
                  <input type="hidden" name="browserInfo" value={getBrowserInfo()} />
                  <input type="hidden" name="pageUrl" value={typeof window !== 'undefined' ? window.location.href : ''} />
                  <input type="hidden" name="submittedAt" value={new Date().toISOString()} />
                  <input type="hidden" name="_subject" value="🐛 Bug Report - Coloriqo" />

                  {/* Privacy Notice */}
                  <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                    <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Privacy:</strong> Your contact info is only used for bug follow-up, never for marketing.
                    </p>
                  </div>

                  {/* Contact Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">Name</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Your name"
                        required
                        className="h-11"
                      />
                      <ValidationError prefix="Name" field="name" errors={state.errors} />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        required
                        className="h-11"
                      />
                      <ValidationError prefix="Email" field="email" errors={state.errors} />
                    </div>
                  </div>

                  {/* Bug Location */}
                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-sm font-medium flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-purple-500" />
                      Where did this happen?
                    </Label>
                    <Input
                      id="location"
                      name="location"
                      placeholder="e.g., Login page, Dashboard header, Settings menu"
                      required
                      className="h-11"
                    />
                  </div>

                  {/* Priority & Category */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="priority" className="text-sm font-medium">Priority</Label>
                      <Select name="priority" value={priority} onValueChange={setPriority}>
                        <SelectTrigger className="h-11">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="z-[10000]">
                          <SelectItem value="low" className="text-green-600">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              Low - Minor issue
                            </div>
                          </SelectItem>
                          <SelectItem value="medium" className="text-yellow-600">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                              Medium - Affects use
                            </div>
                          </SelectItem>
                          <SelectItem value="high" className="text-orange-600">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                              High - Major problem
                            </div>
                          </SelectItem>
                          <SelectItem value="critical" className="text-red-600">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                              Critical - Broken
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category" className="text-sm font-medium">Type</Label>
                      <Select name="category" value={category} onValueChange={setCategory}>
                        <SelectTrigger className="h-11">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="z-[10000]">
                          <SelectItem value="ui">
                            <div className="flex items-center gap-2">
                              <Eye className="w-3 h-3" />
                              Visual/UI
                            </div>
                          </SelectItem>
                          <SelectItem value="functionality">
                            <div className="flex items-center gap-2">
                              <Settings className="w-3 h-3" />
                              Functionality
                            </div>
                          </SelectItem>
                          <SelectItem value="performance">
                            <div className="flex items-center gap-2">
                              <Zap className="w-3 h-3" />
                              Performance
                            </div>
                          </SelectItem>
                          <SelectItem value="mobile">
                            <div className="flex items-center gap-2">
                              <Smartphone className="w-3 h-3" />
                              Mobile
                            </div>
                          </SelectItem>
                          <SelectItem value="security">
                            <div className="flex items-center gap-2">
                              <Shield className="w-3 h-3" />
                              Security
                            </div>
                          </SelectItem>
                          <SelectItem value="other">
                            <div className="flex items-center gap-2">
                              <Wrench className="w-3 h-3" />
                              Other
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Bug Description */}
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">What&apos;s the issue?</Label>
                    <Textarea
                      id="description"
                      name="description"
                      placeholder="Describe what happened and what you expected..."
                      className="min-h-[100px] resize-none"
                      required
                    />
                    <ValidationError prefix="Description" field="description" errors={state.errors} />
                  </div>

                  {/* Steps to Reproduce */}
                  <div className="space-y-2">
                    <Label htmlFor="steps" className="text-sm font-medium">How to reproduce? <span className="text-muted-foreground">(optional)</span></Label>
                    <Textarea
                      id="steps"
                      name="steps"
                      placeholder="1. Go to...&#10;2. Click...&#10;3. See error"
                      className="min-h-[80px] resize-none"
                    />
                  </div>

                  {/* Technical Info Preview */}
                  <div className="flex items-center gap-2 p-2 bg-muted rounded text-xs text-muted-foreground">
                    <Monitor className="w-3 h-3" />
                    <span>Auto-collected: {getBrowserInfo()}</span>
                    <Globe className="w-3 h-3 ml-2" />
                    <span>Current page</span>
                  </div>

                  {/* Show errors if any */}
                  {state.errors && Object.keys(state.errors).length > 0 && (
                    <div className="p-3 bg-red-50 dark:bg-red-950 rounded-lg border border-red-200 dark:border-red-800">
                      <p className="text-sm text-red-800 dark:text-red-200">
                        <strong>Error:</strong> Please check the form fields and try again.
                      </p>
                      {Object.entries(state.errors).map(([field, errors]) => (
                        <div key={field} className="mt-1 text-xs">
                          <strong>{field}:</strong> {Array.isArray(errors) ? errors.join(', ') : errors}
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex gap-4 pt-6 border-t mt-6">
                    <Button
                      type="submit"
                      disabled={state.submitting}
                      className="flex-1 bg-orange-600 hover:bg-orange-700"
                    >
                      {state.submitting ? "Sending..." : "Send Report"}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsOpen(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </div>
          </Card>
        </div>
      )}
    </>
  )
}
