"use client";
import React from "react";
import { FloatingDock } from "@/components/ui/floating-dock";
import { BiSolidCopy } from "react-icons/bi";
import {
  IconBrandXFilled,
  IconBrandInstagramFilled,
  IconBrandWhatsappFilled,
  IconBrandLinkedinFilled,
  IconBrandFacebookFilled,
} from "@tabler/icons-react";

type Props = {
  title?: string;
  text?: string;
  currentUrl?: string; // URL to share, default to "www.wewiselabs.com"
};

export function FloatingDockDemo({
  title = "Coloriqo",
  text = "Check out this product!",
  currentUrl= "https://coloriqo.wewiselabs.com/"
}: Props) {

  const copyLink = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      alert("Link copied to clipboard");
    } catch (err) {
      console.error("Copy failed:", err);
      alert("Could not copy link");
    }
  };

  const enc = (s: string) => encodeURIComponent(s);
  
  const links = [
    {
      title: "Copy Link",
      icon: (
        <div >
        <BiSolidCopy className="h-full w-full text-neutral-500 dark:text-neutral-300"/>
        </div>
      ),
      onClick: copyLink,
    },
    {
      title: "Whatsapp",
      icon: (
        <IconBrandWhatsappFilled className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: `https://wa.me/?text=${enc(title + text + " \n" + currentUrl)}`,
    },
    {
      title: "Linkedin",
      icon: (
        <IconBrandLinkedinFilled className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: `https://www.linkedin.com/sharing/share-offsite/?url=${enc(title + text + " \n" + currentUrl)}`,
    },
    {
      title: "Instagram",
      icon: (
        <IconBrandInstagramFilled className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: `https://https://www.instagram.com/wewiselabs/`,
    },
    {
      title: "Facebook",
      icon: (
        <IconBrandFacebookFilled className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: `https://www.facebook.com/sharer/sharer.php?u=${enc(title + text + " \n" + currentUrl)}`,
    },

    {
      title: "X",
      icon: (
        <IconBrandXFilled className="h-full w-full text-neutral-500 dark:text-neutral-300" />
      ),
      href: `https://twitter.com/intent/tweet?url=${enc(title + text + " \n" + currentUrl)}`,
    },
  ];
  return (
    <div className="flex items-start justify-center w-full -mt-6">
      <FloatingDock
        mobileClassName="translate-y-20" // only for demo, remove for production
        items={links}
      />
    </div>
  );
}
export default FloatingDockDemo;