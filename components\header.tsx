'use client'

import React from 'react'
import { ThemeSwitcher } from "@/components/ui/theme-switcher";
import { LayoutPanelTop, } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useState ,useEffect} from "react";

const Header = () => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false); // State for mobile menu toggle
    const [smallScreen, setSmallScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setSmallScreen(window.innerWidth < 375);
    };

    checkScreenSize(); // Initial check
    window.addEventListener("resize", checkScreenSize);

    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);

    
  return (
    <header className="container mx-auto px-4 py-3 border-b-[1px] border-gray-500/50">
    <div className="flex justify-between items-center">
      {/* Logo - Always visible it's means logo not gonna add on hamburger even in small screen*/}
      <div className="flex items-center gap-2 ml-2">
        <div className="relative">
          <LayoutPanelTop className="h-6 w-6 text-transparent" />
          <div
            className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md"
            style={{
              maskImage:
                "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121'/%3E%3C/svg%3E\")",
              maskSize: "cover",
            }}
          />
        </div>
        <h1
          className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400"
          style={{
            fontFamily: "var(--font-montserrat)",
            letterSpacing: "-0.5px",
            fontWeight: "800",
          }}
        >
          <Link href="/tool" data-barba="wrapper">
            Coloriqo
          </Link>
        </h1>
      </div>

      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center gap-6">
        <nav className="flex gap-8">
          <Link
            href="/#features"
            className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
          >
            Features
          </Link>
          <Link
            href="/#testimonials"
            className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
          >
            Testimonials
          </Link>
          <Link
            href="/pricing"
            className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
          >
            Pricing
          </Link>
        </nav>
        <ThemeSwitcher />
        <Button size="sm">
          <Link href="/tool" data-barba="wrapper">
            Get Started
          </Link>
        </Button>
      </div>

      {/* Mobile Navigation */}
      <div className="flex md:hidden items-center gap-3">
        {/* Theme Switcher - Always visible on mobile .This one is also not gonna add in hamburger*/}
        <ThemeSwitcher />

        {/* Get Started Button - Always visible on mobile .This one too also not gonna add in hamburger*/}
        <Button size="sm" className={`text-xs px-3 ${smallScreen ? "hidden" : ""}`}>
          <Link href="/tool" data-barba="wrapper">
            Get Started
          </Link>
        </Button>

        {/* Hamburger Menu Button */}
        <Button
          variant="outline"
          size="icon"
          className="h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle navigation menu"
          aria-expanded={isMobileMenuOpen}
        >
          <div className="flex flex-col justify-center items-center w-5 h-5 relative">
            {/* Top line! Actually this code is talking about hamburger effects nothing else */}
            <span
              className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform ${
                isMobileMenuOpen
                  ? "rotate-45 translate-y-0"
                  : "-translate-y-1.5"
              }`}
            />

            {/* Middle line */}
            <span
              className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out ${
                isMobileMenuOpen
                  ? "opacity-0 scale-0"
                  : "opacity-100 scale-100"
              }`}
            />

            {/* Bottom line */}
            <span
              className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform ${
                isMobileMenuOpen
                  ? "-rotate-45 translate-y-0"
                  : "translate-y-1.5"
              }`}
            />
          </div>
        </Button>
      </div>
    </div>

    {/* Mobile Menu Overlay - Slides from right to left .It means it's open from right to left */}
    <div
      className={`md:hidden fixed inset-0 z-50 transition-all duration-300 ${
        isMobileMenuOpen ? "visible" : "invisible"
      }`}
    >
      {/* Background overlay */}
      <div
        className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${
          isMobileMenuOpen ? "opacity-100" : "opacity-0"
        }`}
        onClick={() => setIsMobileMenuOpen(false)}
      />

      {/* Menu panel sliding from right */}
      <div
        className={`absolute right-0 top-0 h-full w-64 bg-background border-l shadow-lg transform transition-transform duration-300 ${
          isMobileMenuOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        {/* Menu header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Menu</h2>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-accent transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
            aria-label="Close menu"
          >
            <div className="relative w-4 h-4">
              <span className="absolute block h-0.5 w-4 bg-current rounded-full rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
              <span className="absolute block h-0.5 w-4 bg-current rounded-full -rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
            </div>
          </Button>
        </div>

        {/* Navigation links */}
        <nav className="flex flex-col p-4 space-y-4">
          <Link
            href="/#features"
            className="text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Features
          </Link>
          <Link
            href="/#testimonials"
            className="text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Testimonials
          </Link>
          <Link
            href="/pricing"
            className="text-base font-medium text-muted-foreground hover:text-foreground transition-colors py-2 border-b border-border/50"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Pricing
          </Link>
        </nav>
      </div>
    </div>
  </header>
  )
}

export default Header

