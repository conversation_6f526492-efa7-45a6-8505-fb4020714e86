
import type { <PERSON>ada<PERSON> } from 'next'
import { Mont<PERSON>rat, Rubik, Cinzel_Decorative } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { PageTransitionWrapper } from '@/components/ui/page-transition-wrapper'
import FloatingBugReport from '@/components/floating-bug-report'
import FloatingScrollButton from '@/components/floating-scroll-to-top'

// Use Rubik for main headings (replacing Rubik Doodle Shadow)
const rubik = Rubik({ 
  subsets: ['latin'], 
  variable: '--font-rubik',
  weight: ['400', '500', '600', '700'],
  display: 'swap',
  preload: true
})

// Use Montserrat for secondary headings (replacing Foldit)
const montserrat = Montserrat({ 
  subsets: ['latin'],
  variable: '--font-montserrat',
  weight: ['400', '500', '600', '700', '800'],
  display: 'swap',
  preload: true
})

// Use Cinzel Decorative for elegant accents
const cinzelDecorative = Cinzel_Decorative({
  subsets: ['latin'],
  variable: '--font-cinzel',
  weight: ['400', '700', '900'],
  display: 'swap',
  preload: true
})

// Use Montserrat for body text
const montserratBody = Montserrat({ 
  subsets: ['latin'],
  variable: '--font-montserrat-body',
  weight: ['400', '500', '600', '700', '800'],
  display: 'swap',
  preload: true
})

export const metadata: Metadata = {
  title: 'Coloriqo | Unleash Your Color Story',
  description: "Transform visual inspiration into perfect color palettes with Coloriqo's AI-powered tools",
  keywords: ['color extraction', 'color palette', 'AI', 'design tools', 'Coloriqo'],
  icons: {
    icon: [
      {
        // url: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="%23a855f7" /><circle cx="50" cy="50" r="25" fill="%234f46e5" /></svg>',
        url:'data:image/svg+xml;isUtf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121"/></svg>',
        sizes: '32x32',
        type: 'image/svg+xml'
      }
    ]
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="scroll-smooth">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${montserratBody.variable} ${rubik.variable} ${montserrat.variable} ${cinzelDecorative.variable} font-sans`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <PageTransitionWrapper>
            {children}
          </PageTransitionWrapper>
           <FloatingScrollButton />
          <FloatingBugReport />
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}