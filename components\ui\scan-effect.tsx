"use client"

import React, { useEffect, useState } from "react"

interface ScanEffectProps {
  width: number
  height: number
  duration?: number
}

export const ScanEffect: React.FC<ScanEffectProps> = ({ 
  width, 
  height, 
  duration = 2500 
}) => {
  const [progress, setProgress] = useState(0)
  const [particles, setParticles] = useState<Array<{x: number, y: number, size: number, opacity: number}>>([])
  
  // Generate random particles for the diffusion effect
  useEffect(() => {
    const particleCount = Math.floor((width * height) / 1000)
    const newParticles = []
    
    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        x: Math.random() * width,
        y: Math.random() * height,
        size: Math.random() * 6 + 2,
        opacity: Math.random() * 0.7 + 0.3
      })
    }
    
    setParticles(newParticles)
    
    // Animate the scan progress
    const startTime = Date.now()
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime
      const newProgress = Math.min(elapsed / duration, 1)
      setProgress(newProgress)
      
      if (newProgress >= 1) {
        clearInterval(interval)
      }
    }, 16)
    
    return () => clearInterval(interval)
  }, [width, height, duration])
  
  return (
    <div className="relative overflow-hidden rounded-lg" style={{ width, height }}>
      {/* Background container */}
      <div 
        className="absolute inset-0 bg-background/30 backdrop-blur-sm" 
        style={{ zIndex: 10 }}
      />
      
      {/* Scan line */}
      <div 
        className="absolute bg-primary/30 backdrop-blur-[1px] shadow-lg border-t border-b border-primary/40"
        style={{ 
          left: 0,
          top: `${progress * 100}%`,
          width: '100%',
          height: '3px',
          zIndex: 20,
          boxShadow: '0 0 10px rgba(var(--primary), 0.6)',
          transition: 'top 100ms linear'
        }}
      />
      
      {/* Scan highlight area */}
      <div 
        className="absolute left-0 w-full bg-gradient-to-b from-primary/10 to-transparent" 
        style={{ 
          top: `${progress * 100}%`,
          height: '50px',
          zIndex: 15,
          opacity: 0.5,
          transform: 'translateY(-100%)'
        }}
      />
      
      {/* Particles */}
      {particles.map((particle, index) => (
        <div 
          key={index}
          className="absolute rounded-full bg-primary"
          style={{
            left: particle.x,
            top: Math.min(particle.y, progress * height),
            width: particle.size,
            height: particle.size,
            opacity: particle.opacity * (1 - progress * 0.5), // Fade out as scan progresses
            zIndex: 25,
            transform: `translateY(${progress * 20}px)`,
            transition: 'top 150ms ease-out, opacity 150ms ease-out, transform 150ms ease-out'
          }}
        />
      ))}
      
      {/* Status text */}
      <div 
        className="absolute inset-0 flex flex-col items-center justify-center text-center"
        style={{ zIndex: 30 }}
      >
        <p className="text-lg font-medium mb-1 text-primary">
          Analyzing Image
        </p>
        <p className="text-sm text-muted-foreground">
          {Math.round(progress * 100)}% complete
        </p>
      </div>
    </div>
  )
} 