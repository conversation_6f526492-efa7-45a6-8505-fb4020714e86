"use client";

import { useEffect, useRef, useState, Suspense } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import barba from '@barba/core';
import gsap from 'gsap';

interface PageTransitionProps {
  children: React.ReactNode;
}

// Array of transition dialogue texts
const transitionTexts = [
  "Creative we are!",
  "Design beyond limits.",
  "Imagination in motion.",
  "Art in every pixel.",
  "Your brand, reimagined."
];

export function PageTransition({ children }: PageTransitionProps) {
  return (
    <Suspense fallback={<div className="page-wrapper">{children}</div>}>
      <PageTransitionContent>{children}</PageTransitionContent>
    </Suspense>
  );
}

function PageTransitionContent({ children }: PageTransitionProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [displayChildren, setDisplayChildren] = useState(children);
  const initialized = useRef(false);
  
  // Track route changes for SSR compatibility
  useEffect(() => {
    setDisplayChildren(children);
  }, [children, pathname, searchParams]);

  // Initialize Barba with longer durations
  useEffect(() => {
    if (typeof window !== 'undefined' && !initialized.current) {
      initialized.current = true;
      
      // Create transition layers
      if (!document.querySelector('.transition-container')) {
        const transitionContainer = document.createElement('div');
        transitionContainer.className = 'transition-container';
        
        // Create sliding layers
        for (let i = 0; i < 5; i++) {
          const layer = document.createElement('div');
          layer.className = `transition-layer layer-${i}`;
          transitionContainer.appendChild(layer);
        }
        
        // Create text container
        const textContainer = document.createElement('div');
        textContainer.className = 'transition-text-container';
        
        // Create title element
        const titleElement = document.createElement('div');
        titleElement.className = 'transition-title';
        titleElement.textContent = 'Coloriqo';
        textContainer.appendChild(titleElement);
        
        // Create dialogue element
        const dialogueElement = document.createElement('div');
        dialogueElement.className = 'transition-dialogue';
        dialogueElement.textContent = '';
        textContainer.appendChild(dialogueElement);
        
        transitionContainer.appendChild(textContainer);
        document.body.appendChild(transitionContainer);
      }
      
      // Initialize Barba
      barba.init({
        preventRunning: true,
        transitions: [{
          name: 'sliding-layers-transition',
          
          // This runs before leaving current page
          leave(data) {
            return new Promise(resolve => {
              const done = resolve;
              const current = data.current.container;
              
              // Get random dialogue text
              const randomText = transitionTexts[Math.floor(Math.random() * transitionTexts.length)];
              const dialogueElement = document.querySelector('.transition-dialogue');
              if (dialogueElement) {
                dialogueElement.textContent = randomText;
              }
              
              // Hide current page with longer duration
              gsap.to(current, { opacity: 0, duration: 2.5 });
              
              // Animate transition layers with increased duration
              const tl = gsap.timeline({
                onComplete: done
              });
              
              // Much longer durations for more soothing animations
              tl.to('.layer-0', { xPercent: 0, duration: 3.0, ease: 'power2.inOut' }, 0);
              tl.to('.layer-1', { xPercent: 0, duration: 3.2, ease: 'power2.inOut' }, 0.3);
              tl.to('.layer-2', { xPercent: 0, duration: 3.4, ease: 'power2.inOut' }, 0.6);
              tl.to('.layer-3', { xPercent: 0, duration: 3.6, ease: 'power2.inOut' }, 0.9);
              tl.to('.layer-4', { xPercent: 0, duration: 3.8, ease: 'power2.inOut' }, 1.2);
              
              // Extended text animations with longer delays
              tl.to('.transition-text-container', { opacity: 1, y: 0, duration: 2.0 }, 2.0);
              tl.to('.transition-title', { opacity: 1, y: 0, duration: 2.0 }, 2.4);
              tl.to('.transition-dialogue', { opacity: 1, y: 0, duration: 2.0 }, 2.8);
              
              // Add a deliberate pause at the end to let users enjoy the animation
              tl.to({}, { duration: 1.5 }); // Empty tween just for delay
            });
          },
          
          // This runs when entering the new page
          enter(data) {
            return new Promise(resolve => {
              const done = resolve;
              const next = data.next.container;
              
              gsap.set(next, { opacity: 0 });
              
              const tl = gsap.timeline({
                onComplete: () => {
                  gsap.to(next, { 
                    opacity: 1, 
                    scale: 1, 
                    duration: 2.0,
                    onComplete: done
                  });
                }
              });
              
              // Slower fade out for text with staggered timing
              tl.to('.transition-dialogue', { opacity: 0, y: -20, duration: 2.0 }, 0);
              tl.to('.transition-title', { opacity: 0, y: -20, duration: 2.0 }, 0.5);
              tl.to('.transition-text-container', { opacity: 0, duration: 2.0 }, 1.0);
              
              // Slower layer animations with more staggered timing
              tl.to('.layer-4', { xPercent: 100, duration: 3.0, ease: 'power2.inOut' }, 1.5);
              tl.to('.layer-3', { xPercent: -100, duration: 2.8, ease: 'power2.inOut' }, 1.8);
              tl.to('.layer-2', { xPercent: 100, duration: 2.6, ease: 'power2.inOut' }, 2.1);
              tl.to('.layer-1', { xPercent: -100, duration: 2.4, ease: 'power2.inOut' }, 2.4);
              tl.to('.layer-0', { xPercent: 100, duration: 2.2, ease: 'power2.inOut' }, 2.7);
            });
          },
          
          // This wraps your page content to work with Barba
          beforeEnter(data) {
            // For Next.js compatibility - we're already handling the content with React
            return;
          }
        }]
      });
      // @ts-expect-error: hooks is a valid runtime property on barba
      barba.hooks.after((data: { next: { url: { path: string } } }) => {
        // Dispatch a custom event when Barba's transition is complete
        const event = new CustomEvent('barbaTransitionCompleted', {
          detail: { path: data.next.url.path }
        });
        document.dispatchEvent(event);
      });
    }
  }, []);

  // Implement reduced motion preference support
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleReducedMotionChange = () => {
      if (mediaQuery.matches) {
        // Disable animations for reduced motion preference
        gsap.defaults({ duration: 0.1 });
      } else {
        // Reset to normal animation durations
        gsap.defaults({ duration: 0.5 });
      }
    };
    
    mediaQuery.addEventListener('change', handleReducedMotionChange);
    handleReducedMotionChange(); // Initialize
    
    return () => {
      mediaQuery.removeEventListener('change', handleReducedMotionChange);
    };
  }, []);

  return (
    <div className="page-wrapper" data-barba="container">
      {displayChildren}
    </div>
  );
}

export default PageTransition; 