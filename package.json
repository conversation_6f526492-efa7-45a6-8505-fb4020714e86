{"name": "coloriqo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@barba/core": "^2.10.3", "@formspree/react": "^3.0.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tabler/icons-react": "^3.34.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "cloudinary-video-player": "^3.0.0", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^2.30.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.12", "gsap": "^3.13.0", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "motion": "^12.23.12", "next": "^15.3.3", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "or": "^0.2.0", "prism-react-renderer": "^2.4.1", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "jest": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "typescript-eslint": "^8.39.1"}, "jest": {"testEnvironment": "node", "testMatch": ["**/test/**/*.test.js"]}}