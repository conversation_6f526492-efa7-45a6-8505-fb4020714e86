"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FeatureCard } from "@/components/ui/feature-card";
import { GradientButton } from "@/components/ui/gradient-button";
import {
  Cpu,
  Palette,
  Code,
  ArrowRight,
  MousePointerClick,
  Download,
  Star,
  ChevronRight,
  Copy,
} from "lucide-react";
import Link from "next/link";
import { Contact } from "@/components/contact";
import Header from "@/components/header";
import Footer from "../components/footer";
import HashScroller from "@/components/hashScroller";



// Define features for the app
const FEATURES = [
  {
    icon: Cpu,
    title: "AI-Powered Extraction",
    description:
      "Extract colors intelligently using advanced AI algorithms for optimal palette generation.",
    iconColor:
      "bg-blue-100 text-blue-500 dark:bg-blue-900/50 dark:text-blue-400",
  },
  {
    icon: Palette,
    title: "Custom Color Naming",
    description:
      "Get semantic names for your colors that help communicate and organize your palette.",
    iconColor:
      "bg-purple-100 text-purple-500 dark:bg-purple-900/50 dark:text-purple-400",
  },
  {
    icon: MousePointerClick,
    title: "Precision Picking",
    description:
      "Pixel-perfect color selection with magnified precision tools.",
    iconColor:
      "bg-pink-100 text-pink-500 dark:bg-pink-900/50 dark:text-pink-400",
  },
  {
    icon: Code,
    title: "Export to Code",
    description: "Export your palette directly for immediate use.",
    iconColor:
      "bg-orange-100 text-orange-500 dark:bg-orange-900/50 dark:text-orange-400",
  },
  {
    icon: Copy,
    title: "Copy Formats",
    description: "Copy colors in HEX formats with a single click.",
    iconColor:
      "bg-emerald-100 text-emerald-500 dark:bg-emerald-900/50 dark:text-emerald-400",
  },
  {
    icon: Download,
    title: "One-Click Export",
    description:
      "Save your palettes in various formats for use in all major design tools.",
    iconColor:
      "bg-indigo-100 text-indigo-500 dark:bg-indigo-900/50 dark:text-indigo-400",
  },
];

// Testimonials data
const TESTIMONIALS = [
  {
    quote:
      "Coloriqo changed my workflow completely. I save hours on every project by extracting the perfect palette instantly.",
    name: "Sarah Johnson",
    title: "Senior UI Designer",
    avatar: "https://i.pravatar.cc/150?img=32",
  },
  {
    quote:
      "The AI color naming feature is brilliant. No more struggling to name shades in my design system documentation.",
    name: "Michael Torres",
    title: "Product Designer",
    avatar: "https://i.pravatar.cc/150?img=59",
  },
  {
    quote:
      "As a developer, the code export options are fantastic. Perfect integration with my CSS variables.",
    name: "Leila Khan",
    title: "Frontend Developer",
    avatar: "https://i.pravatar.cc/150?img=48",
  },
];


export default function Home() {
  const [mounted, setMounted] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const [isHoveringDemo, setIsHoveringDemo] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Auto-rotate testimonials
    const interval = setInterval(() => {
      setActiveTestimonial(prev => (prev + 1) % TESTIMONIALS.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <main className="min-h-screen bg-background font-sans antialiased relative overflow-hidden">
      <HashScroller />
      {/* Background overlay */}
      <div className="pointer-events-none fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_center,rgba(var(--primary-rgb),0.08),transparent_70%)]" />

      {/* Top color bar */}
      <div className="h-1.5 w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500" />

      {/* Main content */}
      <div className="relative">
        {/* Header */}
        <Header />
        
        {/* Hero section */}
        <section className="py-20 md:py-28">
          <div className="container mx-auto px-4">
            <div className="flex flex-col items-center text-center max-w-5xl mx-auto">
              <div className="mb-6 inline-flex items-center rounded-full border border-neutral-200 dark:border-neutral-800 px-3 py-1 text-sm gap-1 bg-background shadow-sm">
                <span className="flex h-2 w-2 rounded-full bg-green-500"></span>
                <span className="text-muted-foreground">
                  Now with AI-powered extraction
                </span>
              </div>

              <h1 className="text-4xl md:text-6xl font-heading font-bold tracking-tight mb-6">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 animate-gradient-x bg-size-200">
                  Transform Any Image
                </span>
                <br />
                Into the Perfect Color Palette
              </h1>

              <p className="text-xl text-muted-foreground max-w-3xl mb-10">
                Extract harmonious colors from any image with AI precision.
                Build perfect palettes for your design projects in seconds, not
                hours.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-16">
                <GradientButton
                  size="lg"
                  className="px-8 py-6 font-medium text-base"
                >
                  <Link
                    href="/tool"
                    className="flex items-center gap-2 justify-center"
                    data-barba="wrapper"
                  >
                    Start extracting colors <ArrowRight size={16} />
                  </Link>
                </GradientButton>
                <Button variant="outline" size="lg" className="px-8 py-6">
                  <span className="flex items-center gap-2 justify-center">
                    <Link href="#video">Watch demo</Link>
                  </span>
                </Button>
              </div>

              {/* App Preview */}
              <div className="relative w-full max-w-6xl perspective-1000">
                {/* Shadow */}
                <div className="absolute inset-y-4 -inset-x-4 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 rounded-xl blur-xl -z-10 transition-all duration-500"></div>

                {/* App mockup */}
                <div
                  className="rounded-xl overflow-hidden border border-neutral-200 dark:border-neutral-800 bg-card shadow-2xl transform transition-all duration-500"
                  style={{
                    transform: isHoveringDemo
                      ? "rotateX(2deg) translateY(-4px)"
                      : "rotateX(0) translateY(0)",
                    boxShadow: isHoveringDemo
                      ? "0 25px 50px -12px rgba(0, 0, 0, 0.08)"
                      : "0 10px 30px -15px rgba(0, 0, 0, 0.08)",
                  }}
                  onMouseEnter={() => setIsHoveringDemo(true)}
                  onMouseLeave={() => setIsHoveringDemo(false)}
                >
                  {/* App header */}

                  <div className="flex justify-between items-center p-2 bg-muted/70 border-b border-neutral-200 dark:border-neutral-800">
                    <div className="flex items-center gap-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-400"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                      <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    </div>
                    <div className="text-xs text-muted-foreground font-medium">
                      Coloriqo - Color Extraction Tool
                    </div>
                    <div className="w-16"></div>
                  </div>

                  {/* App content */}
                  <div id="video" className="scroll-mt-12">
                    {/* Video placeholder */}
                    <video
                      src="https://res.cloudinary.com/didt1ywys/video/upload/v1749389530/cursorful-video-1749385801624_jwfjsx.mp4"
                      autoPlay
                      muted
                      playsInline
                      loop
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-24 bg-muted/20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-purple-500">
                  Powerful Features
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Everything you need to extract, refine, and utilize color
                palettes
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {FEATURES.map((feature, index) => (
                <FeatureCard
                  key={index}
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  iconColor={feature.iconColor}
                  className="transition-all duration-300 hover:shadow-md hover:-translate-y-1 border border-neutral-200 dark:border-neutral-800"
                />
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials section */}
        <section id="testimonials" className="py-24">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-orange-500">
                  Loved by Designers & Developers
                </span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                See why professionals choose Coloriqo for their color extraction
                needs
              </p>
            </div>

            <div className="max-w-4xl mx-auto relative">
              {/* Testimonial cards */}
              <div className="overflow-hidden relative h-72">
                {TESTIMONIALS.map((testimonial, index) => (
                  <div
                    key={index}
                    className="absolute inset-0 transition-all duration-500 flex flex-col items-center justify-center p-8 rounded-xl border border-neutral-200 dark:border-neutral-800 bg-card"
                    style={{
                      opacity: index === activeTestimonial ? 1 : 0,
                      transform: `translateX(${(index - activeTestimonial) * 100
                        }%)`,
                      zIndex: index === activeTestimonial ? 10 : 0,
                    }}
                  >
                    <div className="mb-6">
                      {[1, 2, 3, 4, 5].map(star => (
                        <Star
                          key={star}
                          className="inline-block h-4 w-4 text-amber-400 fill-amber-400"
                        />
                      ))}
                    </div>
                    <p className="text-xl font-medium text-center mb-6">
                      "{testimonial.quote}"
                    </p>
                    <div className="flex items-center">
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div>
                        <p className="font-medium">{testimonial.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {testimonial.title}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Testimonial navigation dots */}
              <div className="flex justify-center gap-2 mt-8">
                {TESTIMONIALS.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${index === activeTestimonial
                        ? "bg-primary w-4"
                        : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                      }`}
                    aria-label={`View testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA section */}
        <section className="py-20 bg-muted/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto rounded-2xl bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10 p-8 md:p-12 border border-neutral-200 dark:border-neutral-800 text-center">
              <h3 className="text-3xl md:text-4xl font-heading font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-500">
                  Ready to Transform Your Design Workflow?
                </span>
              </h3>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
                Join thousands of designers extracting perfect color palettes in
                seconds.
              </p>
              <GradientButton
                size="lg"
                className="px-8 py-6 font-medium text-base"
              >
                <Link
                  href="/tool"
                  className="flex items-center gap-2 justify-center"
                  data-barba="wrapper"
                >
                  Start for free{" "}
                  <ChevronRight size={16} className="animate-pulse" />
                </Link>
              </GradientButton>
              <p className="text-sm text-muted-foreground mt-4">
                No credit card required. 10 free extractions included.
              </p>
            </div>
          </div>
        </section>

        {/* Contact  */}
        <Contact />

        {/* Footer */}
        <Footer />
      </div>

    </main>
  );
}
