"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { PageTransition } from "./page-transition";

interface PageTransitionWrapperProps {
  children: React.ReactNode;
}

export function PageTransitionWrapper({ children }: PageTransitionWrapperProps) {
  const router = useRouter();
  const transitionComplete = useRef<(() => void) | null>(null);
  
  // Set up a listener for transition completion
  const onTransitionRequested = (callback: () => void) => {
    // Store the navigation callback for later execution
    transitionComplete.current = callback;
  };
  
  // We're removing the click intercept and letting Barba handle it
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // This function now only handles the execution of the navigation 
    // after Barba's transition completes
    const handleTransitionDone = (event: CustomEvent) => {
      if (event.detail && event.detail.path) {
        const path = event.detail.path;
        console.log("Barba transition complete, navigating to", path);
        
        // Small delay to ensure <PERSON>ba has finished its work
        setTimeout(() => {
          router.push(path);
        }, 100);
      }
    };
    
    // Listen for a custom event that <PERSON><PERSON> will dispatch when done
    document.addEventListener('barbaTransitionCompleted', handleTransitionDone as EventListener);
    
    return () => {
      document.removeEventListener('barbaTransitionCompleted', handleTransitionDone as EventListener);
    };
  }, [router]);
  
  return (
    <PageTransition onTransitionRequested={onTransitionRequested}>
      {children}
    </PageTransition>
  );
}

export default PageTransitionWrapper; 