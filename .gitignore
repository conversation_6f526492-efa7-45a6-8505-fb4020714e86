# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
.env
!.env.example
.env.local
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# OS specific files
.DS_Store
Thumbs.db

# IDE files
.idea/
.vscode/
*.swp
*.swo