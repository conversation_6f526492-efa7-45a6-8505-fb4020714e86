import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

// Utility function for combining class names
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Convert RGB to HEX
export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

// Convert HEX to RGB
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    }
    : null;
}

// Convert RGB to HSL
export function rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
}

// Extended color name mapping with more comprehensive color database
export const COLOR_NAMES: Record<string, string> = {
  // Reds
  red: "#FF0000",
  crimson: "#DC143C",
  maroon: "#800000",
  tomato: "#FF6347",
  coral: "#FF7F50",
  salmon: "#FA8072",
  scarlet: "#FF2400",
  ruby: "#E0115F",
  cherry: "#DE3163",
  brick: "#CB4154",
  burgundy: "#800020",
  wine: "#722F37",
  cardinal: "#C41E3A",
  fire: "#FF4500",

  // Oranges
  orange: "#FFA500",
  gold: "#FFD700",
  amber: "#FFBF00",
  tangerine: "#F28500",
  peach: "#FFCBA4",
  apricot: "#FBCEB1",
  papaya: "#FFEFD5",
  mango: "#FFCC5C",
  pumpkin: "#FF7518",
  rust: "#B7410E",

  // Yellows
  yellow: "#FFFF00",
  khaki: "#F0E68C",
  lemon: "#FFF700",
  canary: "#FFFF99",
  butter: "#FFFF8B",
  cream: "#FFFDD0",
  vanilla: "#F3E5AB",
  honey: "#FFB347",
  mustard: "#FFDB58",
  saffron: "#F4C430",
  goldenrod: "#DAA520",

  // Greens
  green: "#008000",
  lime: "#00FF00",
  olive: "#808000",
  teal: "#008080",
  emerald: "#50C878",
  mint: "#3EB489",
  sage: "#BCB88A",
  forest: "#228B22",
  jungle: "#29AB87",
  pine: "#01796F",
  moss: "#ADDFAD",
  jade: "#00A86B",
  seafoam: "#93E9BE",
  chartreuse: "#7FFF00",
  avocado: "#568203",
  basil: "#355E3B",

  // Blues
  blue: "#0000FF",
  navy: "#000080",
  azure: "#007FFF",
  cyan: "#00FFFF",
  turquoise: "#40E0D0",
  skyblue: "#87CEEB",
  cobalt: "#0047AB",
  royal: "#4169E1",
  steel: "#4682B4",
  powder: "#B0E0E6",
  ice: "#B0E0E6",
  ocean: "#006994",
  denim: "#1560BD",
  sapphire: "#0F52BA",
  periwinkle: "#CCCCFF",
  cornflower: "#6495ED",

  // Purples
  purple: "#800080",
  violet: "#8F00FF",
  magenta: "#FF00FF",
  lavender: "#E6E6FA",
  indigo: "#4B0082",
  plum: "#DDA0DD",
  orchid: "#DA70D6",
  lilac: "#C8A2C8",
  amethyst: "#9966CC",
  grape: "#6F2DA8",
  eggplant: "#614051",
  mauve: "#E0B0FF",

  // Browns
  brown: "#A52A2A",
  chocolate: "#D2691E",
  tan: "#D2B48C",
  beige: "#F5F5DC",
  coffee: "#6F4E37",
  mocha: "#967117",
  caramel: "#AF6E4D",
  cinnamon: "#D2691E",
  chestnut: "#954535",
  mahogany: "#C04000",
  walnut: "#773F1A",
  espresso: "#362D1D",

  // Pinks
  pink: "#FFC0CB",
  rose: "#FF66CC",
  blush: "#DE5D83",
  fuchsia: "#FF00FF",
  hot: "#FF1493",
  bubblegum: "#FF69B4",
  flamingo: "#FC8EAC",
  carnation: "#FFA6C9",

  // Neutrals
  black: "#000000",
  gray: "#808080",
  silver: "#C0C0C0",
  white: "#FFFFFF",
  ivory: "#FFFFF0",
  charcoal: "#36454F",
  slate: "#708090",
  ash: "#B2BEB5",
  pearl: "#EAE0C8",
  platinum: "#E5E4E2",
  smoke: "#738276",
  stone: "#928E85",
  concrete: "#A8A8A8",
  graphite: "#41424C",

  // Metallics
  copper: "#B87333",
  bronze: "#CD7F32",
  brass: "#B5A642",
  pewter: "#96A8A1",
  titanium: "#878681",

  // Earth tones
  sand: "#C2B280",
  clay: "#B66A50",
  terracotta: "#E2725B",
  sienna: "#A0522D",
  umber: "#635147",
  ochre: "#CC7722",
};

// Enhanced color naming function with better accuracy and descriptive names
export function getColorName(hex: string): string {
  const rgb = hexToRgb(hex);
  if (!rgb) return "Unknown";

  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);

  // Determine brightness, saturation, and hue categories
  const brightness = hsl.l;
  const saturation = hsl.s;
  const hue = hsl.h;

  // Handle grayscale colors first
  if (saturation < 5) {
    if (brightness < 10) return "Black";
    if (brightness < 25) return "Charcoal";
    if (brightness < 40) return "Dark Gray";
    if (brightness < 60) return "Gray";
    if (brightness < 75) return "Light Gray";
    if (brightness < 90) return "Silver";
    return "White";
  }

  // Find the closest color name by comparing in LAB color space for better perceptual accuracy
  let minDistance = Number.MAX_VALUE;
  let bestMatch = "";

  for (const [name, colorHex] of Object.entries(COLOR_NAMES)) {
    const namedRgb = hexToRgb(colorHex);
    if (!namedRgb) continue;

    // Use weighted Euclidean distance that considers human perception
    // Red is perceived as brighter, green less so, blue least
    const rWeight = 0.3;
    const gWeight = 0.59;
    const bWeight = 0.11;

    const distance = Math.sqrt(
      rWeight * Math.pow(namedRgb.r - rgb.r, 2) +
      gWeight * Math.pow(namedRgb.g - rgb.g, 2) +
      bWeight * Math.pow(namedRgb.b - rgb.b, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      bestMatch = name;
    }
  }

  // Generate descriptive prefix based on color properties
  let prefix = "";
  const baseName = bestMatch.charAt(0).toUpperCase() + bestMatch.slice(1);

  // If the color is very close to a named color, don't use a prefix
  if (minDistance < 25) {
    return baseName;
  }

  // Add descriptive prefixes based on lightness and saturation
  if (brightness < 15) {
    prefix = "Deep ";
  } else if (brightness < 30) {
    prefix = "Dark ";
  } else if (brightness > 85) {
    prefix = "Pale ";
  } else if (brightness > 70) {
    prefix = "Light ";
  }

  // Add saturation-based prefixes
  if (saturation > 85) {
    prefix = "Vivid " + prefix;
  } else if (saturation > 70) {
    prefix = "Bright " + prefix;
  } else if (saturation < 30) {
    prefix = "Muted " + prefix;
  } else if (saturation < 15) {
    prefix = "Dusty " + prefix;
  }

  // For very unique colors, add hue-based descriptors
  if (minDistance > 60) {
    if (hue >= 0 && hue < 15) prefix = "Warm " + prefix;
    else if (hue >= 345) prefix = "Warm " + prefix;
    else if (hue >= 180 && hue < 240) prefix = "Cool " + prefix;
  }

  return (prefix + baseName).trim();
}

// Offline color naming function that doesn't use API
export function getOfflineColorName(hex: string): string {
  return getColorName(hex);
}
