"use client"

import React, { useState, useRef, useEffect, useMemo, useCallback } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Copy, Check, Download, Cpu, Trash2, LayoutPanelTop, Code, CreditCard, Clock, RefreshCw } from "lucide-react"
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { toast } from "@/hooks/use-toast"
import { useColorExtractor } from "@/hooks/use-color-extractor"
import { Color, ExtractionMethod } from "@/types"
import { CodePreview } from "./ui/code-preview"
import { Magnifier } from "./ui/magnifier"
import { ThemeSwitcher } from "./ui/theme-switcher"
import { ImageUploader } from "@/components/ImageUploader";
import {
  getColorName,
  getOfflineColorName,
  rgbToHex,
} from "@/lib/utils"
import {
  getCreditState,
  // <PERSON><PERSON> to avoid triggering react-hooks/rules-of-hooks for non-hook usage
  useCredit as useCreditService,
  resetCredits,
  formatTimeRemaining,
  getCreditRegenerationPeriod,
  getFullRegenerationTime
} from "@/lib/credit-service"
import Link from "next/link"
import { ColorToolSidebar } from "./color-tool-sidebar"

export default function ColorCards() {
  // Constants
  const MAX_COLORS_LIMIT = 50 // Maximum colors a user can extract
  const ADMIN_CODE = process.env.NEXT_PUBLIC_ADMIN_CODE
  if (!ADMIN_CODE) {
    throw new Error('NEXT_PUBLIC_ADMIN_CODE environment variable is required')
  }
  const COLORS_PER_CREDIT = 5 // Number of colors per credit

  // Constants for session storage
  const SESSION_COLORS_KEY = "color-tool-colors";
  const SESSION_IMAGE_KEY = "color-tool-image";

  // State to track if session storage should be cleared
  const [sessionCleared, setSessionCleared] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Memoize session storage operations with sessionCleared dependency
  const storedColors = useMemo(() => {
    if (typeof window !== 'undefined' && !sessionCleared) {
      try {
        const storedColors = sessionStorage.getItem(SESSION_COLORS_KEY);
        if (storedColors) {
          return JSON.parse(storedColors);
        }
      } catch {
        // Silent fail - start with empty colors
      }
    }
    return [] as Color[];
  }, [sessionCleared]);

  // Memoize image retrieval with sessionCleared dependency
  const storedImage = useMemo(() => {
    if (typeof window !== 'undefined' && !sessionCleared) {
      try {
        return sessionStorage.getItem(SESSION_IMAGE_KEY);
      } catch {
        // Silent fail - start with no image
      }
    }
    return null;
  }, [sessionCleared]);

// Rehydrate selectedFile from stored image on refresh
useEffect(() => {
  if (!selectedFile && storedImage?.startsWith("data:image")) {
    try {
      const byteString = atob(storedImage.split(',')[1]);
      const mimeString = storedImage.split(',')[0].split(':')[1].split(';')[0];

      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      const blob = new Blob([ab], { type: mimeString });
      const file = new File([blob], "restored-image.png", { type: mimeString });

      setSelectedFile(file); //  This enables the AI button
      setSelectedImage(storedImage); // For safety (UI)
    } catch {
    }
  }
}, [storedImage, selectedFile, setSelectedImage]) // Added setSelectedImage to satisfy exhaustive-deps lint rule


  // States
  const [extractedColors, setExtractedColors] = useState<Color[]>(storedColors)
  const [copiedColor, setCopiedColor] = useState<string | null>(null)
  const [extractionMethod, setExtractionMethod] = useState<ExtractionMethod>("ai")
  const [activeTab, setActiveTab] = useState<string>("upload")
  const [showCodePreview, setShowCodePreview] = useState(false)
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null)
  const [showMagnifier, setShowMagnifier] = useState(false)
  const [currentPixelColor, setCurrentPixelColor] = useState<string>("")
  const [isAdminMode, setIsAdminMode] = useState(false)
  const [adminCodeInput, setAdminCodeInput] = useState("")
  const [showAdminInput, setShowAdminInput] = useState(false)
  const [creditState, setCreditState] = useState(() => getCreditState())
  const [timeRemaining, setTimeRemaining] = useState<string>("")
  const [manualPicksCount, setManualPicksCount] = useState(0) // Count manual color picks
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false) // State for mobile menu toggle
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true) // State for sidebar collapse - default collapsed on mobile
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Create ref outside of useEffect to track credit state
  const creditStateRef = useRef(creditState);

  // Update ref whenever creditState changes
  useEffect(() => {
    creditStateRef.current = creditState;
  }, [creditState]);

  // Update credit info and handle timer countdown
  useEffect(() => {
    // Initial state fetch
    const initialState = getCreditState(true);
    setCreditState(initialState);

    // Display and update timer
    const timerInterval = setInterval(() => {
      // Get current credit state
      const currentState = getCreditState(false);

      // If there's a regeneration time, show countdown
      if (currentState.nextRegenerationTime > 0) {
        // Calculate and display time remaining
        const timeLeft = formatTimeRemaining(currentState.nextRegenerationTime);
        setTimeRemaining(timeLeft);

        // Check if time is up (credits should regenerate)
        if (timeLeft === "now") {
          // Force refresh to get updated credits
          const refreshedState = getCreditState(true);

          // Only update state if credits actually changed
          if (refreshedState.credits !== creditStateRef.current.credits) {
            setCreditState(refreshedState);

            // Show notification only if credits increased
            if (refreshedState.credits > creditStateRef.current.credits) {
              toast({
                title: "Credits refreshed!",
                description: "You have new credits available.",
                duration: 3000,
              });
            }
          }
        }
      } else {
        // No regeneration time, clear the timer display
        setTimeRemaining("");
      }

      // Only update credit state if it has actually changed
      if (currentState.credits !== creditStateRef.current.credits ||
        currentState.nextRegenerationTime !== creditStateRef.current.nextRegenerationTime) {
        setCreditState(currentState);
      }
    }, 1000); // Update every second

    return () => {
      clearInterval(timerInterval);
    };
  }, []); // No dependencies to avoid re-creating the interval

  // Update credit state - memoize this function to improve performance
  const updateCreditState = useCallback((forceRefresh: boolean = false) => {
    const newState = getCreditState(forceRefresh);
    setCreditState(newState);
    return newState;
  }, []);

  // Show current credit status
  const showCreditStatus = () => {
    const state = updateCreditState();

    toast({
      title: "Credit Status",
      description: (
        <div className="flex flex-col gap-1">
          <p>Available credits: {state.credits}</p>
          {state.nextRegenerationTime > 0 && (
            <p>Credits will reset at: {formatTimeRemaining(state.nextRegenerationTime)}</p>
          )}
          {state.nextRegenerationTime > 0 && (
            <p className="text-xs text-muted-foreground mt-1">{getFullRegenerationTime(state.nextRegenerationTime)}</p>
          )}
        </div>
      ),
      duration: 7000,
    });
  };

  // Use a credit for extraction
  const consumeCredit = () => {
    if (isAdminMode) {
      return true; // Admin has unlimited credits
    }

    // Get fresh state directly from service
    const currentState = getCreditState(true);

    if (currentState.credits > 0) {
      try {
        // Use credit and get new state
        // Use service function (not a React hook) to avoid hooks lint error
        const newState = useCreditService();

        // Update component state
        setCreditState(newState);

        // Show toast to confirm credit usage
        toast({
          title: "Credit used",
          description: `1 credit consumed. ${newState.credits} credits remaining.`,
          duration: 3000,
        });

        return true;
      } catch {
        toast({
          title: "Error",
          description: "Could not process credit. Please try again.",
          variant: "destructive",
          duration: 3000,
        });
        return false;
      }
    }
    return false;
  };

  // Update credits based on admin status
  useEffect(() => {
    if (isAdminMode) {
      // Reset credits if admin mode is activated
      resetCredits();
      updateCreditState();
    }
  }, [isAdminMode, updateCreditState]); // Include updateCreditState as dependency to satisfy exhaustive-deps

  // Listen for custom event to open admin input
  useEffect(() => {
    const handleOpenAdminInput = () => {
      setShowAdminInput(true)
    }
    window.addEventListener('openAdminInput', handleOpenAdminInput)
    return () => {
      window.removeEventListener('openAdminInput', handleOpenAdminInput)
    }
  }, [])

  // Clean up any resources on unmount
  useEffect(() => {
    return () => {
      // Clean up any ObjectURLs or other resources
      if (selectedFile) {
        setSelectedFile(null)
      }
    }
  }, [selectedFile]) // Include selectedFile to satisfy exhaustive-deps for cleanup references

  // Save colors to session storage when they change - debounced to reduce writes
  useEffect(() => {
    // Use a timeout to debounce multiple quick updates
    const saveTimeout = setTimeout(() => {
      if (typeof window !== 'undefined' && extractedColors.length > 0) {
        try {
          sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(extractedColors));
        } catch {
          // Silent fail - colors will be lost on refresh
        }
      }
    }, 300); // Debounce for 300ms

    // Clear timeout on cleanup
    return () => clearTimeout(saveTimeout);
  }, [extractedColors]);

  // Use our custom hook for color extraction
  const {
    isExtracting,
    selectedImage,
    setSelectedImage,
    isPickingColor,
    extractionError,
    imageSize,
    toggleColorPicker,
    handleFileSelect,
    extractColors,
  } = useColorExtractor({
    onExtractedColors: (colors) => {
      setExtractedColors(prev => {
        const newColors = [...prev, ...colors];
        // Save to session storage if not cleared
        if (!sessionCleared && typeof window !== 'undefined') {
          sessionStorage.setItem(SESSION_COLORS_KEY, JSON.stringify(newColors));
        }
        return newColors;
      });
    },
    canvasRef,
    existingColors: extractedColors,
    isAdminMode,
    colorLimit: MAX_COLORS_LIMIT,
    initialImage: storedImage,
  });

  // Save image to session storage when it changes - debounced
  useEffect(() => {
    // Debounce to prevent excessive writes
    const saveTimeout = setTimeout(() => {
      if (typeof window !== 'undefined' && selectedImage && !sessionCleared) {
        try {
          sessionStorage.setItem(SESSION_IMAGE_KEY, selectedImage);
        } catch {
          // Silent fail - image will be lost on refresh
        }
      }
    }, 300); // Debounce for 300ms

    return () => clearTimeout(saveTimeout);
  }, [selectedImage, sessionCleared]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Auto-collapse on mobile, auto-expand on desktop
      if (window.innerWidth < 768) {
        setIsSidebarCollapsed(true)
      } else {
        setIsSidebarCollapsed(false)
      }
    }

    // Set initial state
    handleResize()

    // Listen for resize events
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle mouse movement over the canvas
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || extractionMethod !== "manual") return

    const canvas = canvasRef.current
    const rect = canvas.getBoundingClientRect()

    // Calculate the cursor position relative to the canvas
    const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1)
    const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1)

    setMousePosition({ x, y })

    // Get the pixel color under the cursor
    try {
      const ctx = canvas.getContext("2d")
      if (ctx) {
        const pixelData = ctx.getImageData(x, y, 1, 1).data
        const pixelColor = rgbToHex(pixelData[0], pixelData[1], pixelData[2])
        setCurrentPixelColor(pixelColor)
      }
    } catch {
      // Silent fail - color picker will not show current color
    }
  }

  // Calculate magnifier position to keep it within viewport bounds
  const getMagnifierPosition = (x: number, y: number, canvasRect: DOMRect, size: number) => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // Default position (to the right of cursor)
    let posX = x + 20
    let posY = y - 75

    // Adjust if too close to right edge
    if (posX + size > viewportWidth - 20) {
      posX = x - size - 20 // Position to the left of cursor
    }

    // Adjust if too close to bottom edge
    if (posY + size > viewportHeight - 20) {
      posY = viewportHeight - size - 20
    }

    // Adjust if too close to top edge
    if (posY < 20) {
      posY = 20
    }

    return { left: posX, top: posY }
  }

  // Handle mouse enter on canvas
  const handleMouseEnter = () => {
    if (extractionMethod === "manual" && isPickingColor && (isAdminMode || creditState.credits > 0)) {
      setShowMagnifier(true)
    }
  }

  // Handle mouse leave on canvas
  const handleMouseLeave = () => {
    setShowMagnifier(false)
    setMousePosition(null)
    setCurrentPixelColor("")
  }

  // Get all colors for display and export - memoized to prevent recalculations
  const allColors = useMemo(() => extractedColors, [extractedColors])

  // Trigger file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // Handle file change with additional security checks
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check file size (limit to 5MB)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      if (file.size > MAX_FILE_SIZE) {
        toast({
          title: "File too large",
          description: "The image must be smaller than 5MB",
          variant: "destructive"
        })
        return
      }

      // Check file type - expanded list of supported formats
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'image/svg+xml',
        'image/x-icon',
        'image/vnd.microsoft.icon',
        'image/heic',
        'image/heif',
        'image/avif',
        'image/jp2',
        'image/jpx',
        'image/jpm',
        'image/jxl'
      ];

      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please upload a supported image format (JPEG, PNG, WebP, GIF, BMP, TIFF, SVG, ICO, HEIC, AVIF, JPEG2000, JPEG XL)",
          variant: "destructive"
        })
        return
      }

      // Validate image dimensions
      const img = new Image();
      img.onload = () => {
        const MAX_DIMENSION = 4096; // Maximum width or height
        if (img.width > MAX_DIMENSION || img.height > MAX_DIMENSION) {
          toast({
            title: "Image too large",
            description: "Image dimensions must be less than 4096x4096 pixels",
            variant: "destructive"
          });
          return;
        }

      const selectedFile = handleFileSelect(file);
      if (selectedFile) {
          setSelectedFile(selectedFile);
        }
      };

      img.onerror = () => {
        toast({
          title: "Invalid image",
          description: "The file appears to be corrupted or invalid",
          variant: "destructive"
        });
      };
      setSelectedFile(file);

      const reader = new FileReader()
      reader.onload = () => {
       const base64Data = reader.result as string
       setSelectedImage(base64Data)
       sessionStorage.setItem(SESSION_IMAGE_KEY, base64Data)
       }
      reader.readAsDataURL(file)
    }
  }

  // Name colors using AI
  const nameColorsWithAI = async (hexColors: string[]): Promise<{ hex: string, name: string }[]> => {
    try {
      const response = await fetch("/api/name-colors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ colors: hexColors }),
      });

      if (!response.ok) {
        throw new Error("Failed to name colors");
      }

      const data = await response.json();
      return data.colors;
    } catch {
      // Fallback to local naming if AI fails
      return hexColors.map(hex => ({
        hex,
        name: getColorName(hex)
      }));
    }
  }

  // Handle starting color extraction with the selected method
  const handleExtract = async () => {
    if (selectedFile) {
      // Force refresh credit state before checking
      const freshState = updateCreditState(true);

      // Check if we have credits available or are in admin mode
      if (isAdminMode || freshState.credits > 0) {
        // For AI extraction, we'll consume 1 credit for up to 5 colors
        if (extractionMethod === "ai" && !isAdminMode) {
          // Always consume a credit BEFORE extraction for AI method
          const creditConsumed = consumeCredit();

          if (!creditConsumed) {
            toast({
              title: "Error",
              description: "Could not process credit. Please try again.",
              variant: "destructive",
              duration: 3000,
            });
            return;
          }

          // Show a toast notification about AI extracting colors
          toast({
            title: "AI is analyzing your image",
            description: "Extracting the most prominent and visually important colors...",
            duration: 5000,
          });

          await extractColors(selectedFile, extractionMethod);
        } else {
          await extractColors(selectedFile, extractionMethod);
        }
      } else {
        // If no credits, show toast with info about regeneration
        toast({
          title: "No credits available",
          description: (
            <div className="flex flex-col gap-2">
              <p>Credits will be available at {timeRemaining}.</p>
              <div className="text-xs mt-1 cursor-pointer text-blue-500" onClick={toggleAdminInput}>
                Upgrade for unlimited access
              </div>
            </div>
          ),
          duration: 5000,
        });
      }
    } else {
      toast({
        title: "No image selected",
        description: "Please upload an image first.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Extract a single color from the canvas when clicked
  const handleCanvasClick = async (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;

    // Check if we're in manual mode, if not, don't do anything
    if (extractionMethod !== "manual") return;

    // Check if we've reached the maximum color limit
    if (extractedColors.length >= MAX_COLORS_LIMIT) {
      toast({
        title: "Color limit reached",
        description: `You've reached the maximum limit of ${MAX_COLORS_LIMIT} colors. Remove some colors to extract more.`,
        variant: "destructive",
        duration: 5000,
      });
      return;
    }

    // Check if we have credits available or are in admin mode
    if (!isAdminMode && creditState.credits === 0) {
      toast({
        title: "No credits available",
        description: (
          <div className="flex flex-col gap-2">
            <p>Credits will be available at {timeRemaining}.</p>
            <div className="text-xs mt-1 cursor-pointer text-blue-500" onClick={toggleAdminInput}>
              Upgrade for unlimited access
            </div>
          </div>
        ),
        duration: 5000,
      });
      return;
    }

    try {
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();
      const x = Math.min(Math.max(0, e.clientX - rect.left), canvas.width - 1);
      const y = Math.min(Math.max(0, e.clientY - rect.top), canvas.height - 1);

      const ctx = canvas.getContext("2d");
      if (ctx) {
        const pixelData = ctx.getImageData(x, y, 1, 1).data;
        const hex = rgbToHex(pixelData[0], pixelData[1], pixelData[2]);

        // Check if this color already exists
        if (extractedColors.some(color => color.hex.toLowerCase() === hex.toLowerCase())) {
          toast({
            title: "Duplicate color",
            description: (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                <span>This color is already in your palette</span>
              </div>
            ),
            duration: 3000,
          });
          return;
        }

        // For manual picking, only consume a credit every COLORS_PER_CREDIT colors
        if (!isAdminMode) {
          const newPicksCount = manualPicksCount + 1;
          setManualPicksCount(newPicksCount);

          // If we've reached the threshold, consume a credit and reset the counter
          if (newPicksCount >= COLORS_PER_CREDIT) {
            const creditConsumed = consumeCredit();

            if (!creditConsumed) {
              toast({
                title: "Credit deduction failed",
                description: "Could not deduct credit. Please try again.",
                variant: "destructive",
                duration: 3000,
              });
              return;
            }

            setManualPicksCount(0);

            // Credit usage toast is now handled in consumeCredit function
          } else {
            // Show remaining picks
            toast({
              title: "Color extracted!",
              description: (
                <div className="flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                    <span>{hex}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {COLORS_PER_CREDIT - newPicksCount} more picks until 1 credit is used
                  </p>
                </div>
              ),
              duration: 2000,
            });
          }
        }

        // Use offline color naming for manual selection to optimize API usage
        const colorName = getOfflineColorName(hex);
        const tempColorId = Date.now().toString();
        const newColor = { name: colorName, hex, id: tempColorId };
        
        setExtractedColors((prev) => [...prev, newColor]);
        const tempColor = { name: "Naming...", hex, id: tempColorId };
        setExtractedColors((prev) => [...prev, tempColor]);

        // Get color name from AI
        try {
          const namedColors = await nameColorsWithAI([hex]);
          if (namedColors && namedColors.length > 0) {
            // Update the color with the AI-generated name
            setExtractedColors((prev) =>
              prev.map(color =>
                color.id === tempColorId
                  ? { ...namedColors[0], id: tempColorId }
                  : color
              )
            );

            // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)
            if (isAdminMode) {
              toast({
                title: "Color extracted!",
                description: (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                    <span>{namedColors[0].name} ({hex})</span>
                  </div>
                ),
                duration: 3000,
              });
            }
          }
        } catch {
          // If AI naming fails, use the local getColorName function as fallback
          const colorName = getColorName(hex);
          setExtractedColors((prev) =>
            prev.map(color =>
              color.id === tempColorId
                ? { name: colorName, hex, id: tempColorId }
                : color
            )
          );

          // Only show toast if we're in admin mode (otherwise it's shown in the credit handling code)
          if (isAdminMode) {
            toast({
              title: "Color extracted!",
              description: (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: hex }}></div>
                  <span>{colorName} ({hex})</span>
                </div>
              ),
              duration: 3000,
            });
          }
        }
      }
    } catch {
      toast({
        title: "Error extracting color",
        description: "Could not read pixel data from the image",
        variant: "destructive"
      });
    }
  };

  // Copy color to clipboard with error handling
  const copyColor = (hex: string) => {
    try {
      navigator.clipboard.writeText(hex).then(() => {
        setCopiedColor(hex)
        toast({
          title: "Color copied!",
          description: `${hex} has been copied to clipboard.`,
          duration: 2000,
        })

        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopiedColor(null)
        }, 2000)
      }).catch(() => {
        toast({
          title: "Failed to copy",
          description: "Could not copy to clipboard. Try again or copy manually.",
          variant: "destructive"
        })
      })
    } catch {
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard. Try again or copy manually.",
        variant: "destructive"
      })
    }
  }

  // Show code preview 
  const handleShowCodePreview = () => {
    if (allColors.length === 0) {
      toast({
        title: "No colors to export",
        description: "Extract some colors first before viewing code.",
        variant: "destructive"
      })
      return
    }

    // Color codes should always be available to view (not premium)
    setShowCodePreview(true)
  }

  // Export palette as a file
  const exportPalette = () => {
    if (allColors.length === 0) {
      toast({
        title: "No colors to export",
        description: "Extract some colors first before exporting.",
        variant: "destructive"
      })
      return
    }

    try {
      const jsonContent = JSON.stringify(allColors, null, 2)
      const blob = new Blob([jsonContent], { type: "application/json" })
      const url = URL.createObjectURL(blob)

      const a = document.createElement("a")
      a.href = url
      a.download = "color-palette.json"
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Palette exported!",
        description: "Color palette has been exported as JSON file.",
        duration: 3000,
      })
    } catch  {
      toast({
        title: "Export failed",
        description: "Could not export the color palette. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Remove a color from extracted colors
  const removeColor = (index: number) => {
    setExtractedColors(prev => prev.filter((_, i) => i !== index))
    toast({
      title: "Color removed",
      description: "The color has been removed from your palette.",
      duration: 2000,
    })
  }

  // Clear all extracted colors
  const clearAllColors = () => {
    if (extractedColors.length === 0) return

    setExtractedColors([])
    // Clear colors from session storage
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(SESSION_COLORS_KEY);
    }
    setSessionCleared(true);

    toast({
      title: "Colors cleared",
      description: "All extracted colors have been cleared.",
      duration: 2000,
    })
  }

  // Remove the image
  const removeImage = () => {
    setSelectedFile(null);
    setSelectedImage(null);
    // Clear image from session storage
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(SESSION_IMAGE_KEY);
    }
    setSessionCleared(true);

    toast({
      title: "Image removed",
      description: "The image has been removed.",
      duration: 2000,
    })
  }

  // Toggle the color picker with magnifier setup
  // const handleToggleColorPicker = () => {
  //   const newState = !isPickingColor
  //   toggleColorPicker()

  //   // If we're turning off the color picker, hide magnifier
  //   if (!newState) {
  //     setShowMagnifier(false)
  //   }
  // }

  // Handle extraction method change
  const handleExtractionMethodChange = (value: string) => {
    const method = value as ExtractionMethod
    setExtractionMethod(method)

    if (method === "manual") {
      // Always enable color picking mode when switching to manual
      if (!isPickingColor) {
        toggleColorPicker();
      }

      toast({
        title: "Manual mode activated",
        description: (
          <div className="flex items-center gap-2">
            <Pipette className="h-4 w-4" />
            <span>Click anywhere on the image to pick colors</span>
          </div>
        ),
        duration: 3000
      })
    } else {
      // When switching to AI mode, hide magnifier and disable color picking if active
      setShowMagnifier(false)
      if (isPickingColor) {
        toggleColorPicker();
      }
    }
  }

  // Verify admin code
  const verifyAdminCode = (code: string) => {
    if (code === ADMIN_CODE) {
      setIsAdminMode(true)
      setShowAdminInput(false)
      // Reset credits for admin (unlimited)
      resetCredits()
      updateCreditState()
      toast({
        title: "Admin mode activated",
        description: "No color extraction limits applied",
        duration: 3000
      })
    } else {
      toast({
        title: "Invalid code",
        description: "The code entered is not valid",
        variant: "destructive",
        duration: 3000
      })
    }
    setAdminCodeInput("")
  }

  // Toggle admin input visibility
  const toggleAdminInput = () => {
    setShowAdminInput(prev => !prev)
  }

  return (
    <TooltipProvider>
      <div className="fixed inset-0 flex flex-col bg-background overflow-hidden">
        {/* App header */}
        <header className="border-b bg-card p-4 flex items-center justify-between shadow-sm">
          <div className="flex items-center gap-2">
            {/* Logo - Always visible */}
            <div className="flex items-center gap-2 ml-2">
              <div className="relative">
                <LayoutPanelTop className="h-6 w-6 text-transparent" />
                <div className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md" style={{ maskImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121\'/%3E%3C/svg%3E")', maskSize: 'cover' }} />
              </div>
              <h1 className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400" style={{ fontFamily: "var(--font-montserrat)", letterSpacing: "-0.5px", fontWeight: "800" }}>
                <Link href="/" data-barba="wrapper" className="hidden min-[360px]:inline">{"Coloriqo"}</Link>
              </h1>
            </div>
            {isAdminMode && (
              <div className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium">
                Admin
              </div>
            )}
          </div>
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center gap-4">
            {/* Credits display */}
            <div className="flex items-center gap-2 px-3 py-1 border rounded-md bg-background">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                {isAdminMode ? "∞" : creditState.credits} Credits
              </span>
              {creditState.credits === 0 && !isAdminMode && timeRemaining && (
                <div className="flex items-center text-xs text-muted-foreground ml-1">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Available at {timeRemaining}</span>
                </div>
              )}
              {!isAdminMode && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 ml-1 px-2 text-xs text-blue-500"
                  onClick={showCreditStatus}
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Status
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <ThemeSwitcher />
              {/* {isAdminMode ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200"
                  onClick={() => {
                    setIsAdminMode(false);
                    toast({
                      title: "Logged out",
                      description: "Returned to standard user mode",
                      duration: 3000
                    });
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" /> Admin Logout
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleAdminInput}
                >
                  <LogIn className="mr-2 h-4 w-4" /> Admin Login
                </Button>
              )} */}
              {/* Server-side rendering safe buttons with consistent disabled state */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowCodePreview}
                disabled={extractedColors.length === 0 ? true : false}
              >
                <Code className="mr-2 h-4 w-4" /> View Code
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportPalette}
                disabled={extractedColors.length === 0 ? true : false}
              >
                <Download className="mr-2 h-4 w-4" /> Export
              </Button>
              {extractedColors.length > 0 ? (
                <Button variant="outline" size="sm" onClick={clearAllColors}>
                  <Trash2 className="mr-2 h-4 w-4" /> Clear All
                </Button>
              ) : null}
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="flex md:hidden items-center gap-2">
            {/* Credits display - Always visible on mobile */}
            <div className="flex items-center gap-1 px-2 py-1 border rounded-md bg-background text-xs">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="font-medium">
                {isAdminMode ? "∞" : creditState.credits}
              </span>
              <p> {creditState.credits === 1 ? "Credit" : "Credits"}</p>
            </div>

            {/* Theme Switcher - Always visible on mobile */}
            <ThemeSwitcher />

            {/* Hamburger Menu Button */}
            <Button
              variant="outline"
              size="icon"
              className="h-9 w-9 relative overflow-hidden group hover:bg-accent transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle navigation menu"
              aria-expanded={isMobileMenuOpen}
            >
              <div className="flex flex-col justify-center items-center w-5 h-5 relative">
                {/* Top line */}
                <span className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform ${isMobileMenuOpen
                  ? 'rotate-45 translate-y-0'
                  : '-translate-y-1.5'
                  }`} />

                {/* Middle line */}
                <span className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out ${isMobileMenuOpen
                  ? 'opacity-0 scale-0'
                  : 'opacity-100 scale-100'
                  }`} />

                {/* Bottom line */}
                <span className={`absolute block h-0.5 w-5 bg-current rounded-full transition-all duration-300 ease-in-out transform ${isMobileMenuOpen
                  ? '-rotate-45 translate-y-0'
                  : 'translate-y-1.5'
                  }`} />
              </div>
            </Button>
          </div>

          {/* Mobile Menu Overlay - Slides from right to left */}
          <div className={`md:hidden fixed inset-0 z-50 w-full h-full overflow-hidden overscroll-none touch-none transition-all duration-300 ${isMobileMenuOpen ? 'visible pointer-events-auto' : 'invisible pointer-events-none'}`} role="dialog" aria-modal="true" style={{width: '100vw', height: '100vh', maxWidth: '100vw', maxHeight: '100vh', overflowX: 'hidden', overscrollBehaviorX: 'none'}}>
            {/* Background overlay */}
            <div
              className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${isMobileMenuOpen ? 'opacity-100' : 'opacity-0'}`}
              onClick={() => setIsMobileMenuOpen(false)}
              style={{overflowX: 'hidden', overscrollBehaviorX: 'none', touchAction: 'none'}}
            />

            {/* Menu panel sliding from right */}
            <div className={`fixed right-0 top-0 h-full w-80 max-w-[88vw] bg-background border-l shadow-lg transform transition-transform duration-300 ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`} style={{overflowX: 'hidden', overscrollBehaviorX: 'none', touchAction: 'pan-y'}}>
              {/* Menu header */}
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">Tools</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-accent transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                  aria-label="Close menu"
                >
                  <div className="relative w-4 h-4">
                    <span className="absolute block h-0.5 w-4 bg-current rounded-full rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                    <span className="absolute block h-0.5 w-4 bg-current rounded-full -rotate-45 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                  </div>
                </Button>
              </div>

              {/* Menu content */}
              <div className="flex flex-col p-4 space-y-4">
                {/* Admin Section */}
                {/* <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Admin</h3>
                  {isAdminMode ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-200"
                      onClick={() => {
                        setIsAdminMode(false);
                        setIsMobileMenuOpen(false);
                        toast({
                          title: "Logged out",
                          description: "Returned to standard user mode",
                          duration: 3000
                        });
                      }}
                    >
                      <LogOut className="mr-2 h-4 w-4" /> Admin Logout
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        toggleAdminInput();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <LogIn className="mr-2 h-4 w-4" /> Admin Login
                    </Button>
                  )}
                </div> */}

                {/* Tools Section */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Tools</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      handleShowCodePreview();
                      setIsMobileMenuOpen(false);
                    }}
                    disabled={extractedColors.length === 0 ? true : false}
                  >
                    <Code className="mr-2 h-4 w-4" /> View Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      exportPalette();
                      setIsMobileMenuOpen(false);
                    }}
                    disabled={extractedColors.length === 0 ? true : false}
                  >
                    <Download className="mr-2 h-4 w-4" /> Export
                  </Button>
                  {extractedColors.length > 0 ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        clearAllColors();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" /> Clear All
                    </Button>
                  ) : null}
                </div>

                {/* Credits Section */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Credits</h3>
                  <div className="flex items-center gap-2 px-3 py-2 border rounded-md bg-background">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">
                      {isAdminMode ? "∞" : creditState.credits} Credits
                    </span>
                  </div>
                  {creditState.credits === 0 && !isAdminMode && timeRemaining && (
                    <div className="flex items-center text-xs text-muted-foreground px-3 py-2 border rounded-md bg-muted/50">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>Available at {timeRemaining}</span>
                    </div>
                  )}
                  {!isAdminMode && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full text-blue-500"
                      onClick={() => {
                        showCreditStatus();
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Check Status
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar Component */}
          <ColorToolSidebar
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            extractedColors={extractedColors}
            selectedImage={selectedImage}
            selectedFile={selectedFile}
            extractionMethod={extractionMethod}
            currentPixelColor={currentPixelColor}
            isAdminMode={isAdminMode}
            showAdminInput={showAdminInput}
            adminCodeInput={adminCodeInput}
            creditState={creditState}
            timeRemaining={timeRemaining}
            imageSize={imageSize}
            copiedColor={copiedColor}
            isExtracting={isExtracting}
            extractionError={extractionError}
            isSidebarCollapsed={isSidebarCollapsed}
            setIsSidebarCollapsed={setIsSidebarCollapsed}
            handleUploadClick={handleUploadClick}
            handleFileChange={handleFileChange}
            handleExtractionMethodChange={handleExtractionMethodChange}
            handleExtract={handleExtract}
            toggleAdminInput={toggleAdminInput}
            getCreditRegenerationPeriod={getCreditRegenerationPeriod}
            copyColor={copyColor}
            removeColor={removeColor}
            clearAllColors={clearAllColors}
            handleShowCodePreview={handleShowCodePreview}
            fileInputRef={fileInputRef}
          />

          {/* Main content */}
          <main className="flex-1 p-6 overflow-y-auto">
            {selectedImage ? (
              <div className="relative flex justify-center py-8">
                <canvas
                  ref={canvasRef}
                  onClick={handleCanvasClick}
                  onMouseMove={handleMouseMove}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  className={`border border-border rounded-lg max-w-full shadow-md ${isPickingColor ? "cursor-crosshair" : ""}`}
                  style={{ maxHeight: "70vh" }}
                />

                {/* Remove image button */}
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute -top-1  -right-3 sm:right-5 h-6 w-6 sm:h-8 sm:w-8 shadow-md z-10"
                  onClick={removeImage}
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>

                {isPickingColor && (
                  <div className="absolute -top-2 left-30 bg-background/90 text-foreground px-3 py-1.5 rounded-md text-xs sm:text-sm shadow-xl border border-border">
                    Click on the image to extract colors
                  </div>
                )}
                {isPickingColor && showMagnifier && mousePosition && canvasRef.current && (
                  <div
                    className="pointer-events-none fixed"
                    style={{
                      position: 'absolute',
                      left: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).left,
                      top: getMagnifierPosition(mousePosition.x, mousePosition.y, canvasRef.current.getBoundingClientRect(), 150).top,
                      zIndex: 50
                    }}
                  >
                    <Magnifier
                      sourceCanvas={canvasRef.current}
                      x={mousePosition.x}
                      y={mousePosition.y}
                      zoomLevel={5}
                      size={150}
                    />
                  </div>
                )}

                {/* AI extraction overlay */}
                {isExtracting && extractionMethod === "ai" && (
                  <div className="absolute inset-0 bg-background/70 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border">
                    <div className="relative">
                      <Cpu className="h-12 w-12 text-primary animate-pulse mb-4" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-6 w-6 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
                      </div>
                    </div>
                    <h3 className="text-lg font-medium mb-2">AI Analyzing Image</h3>
                    <p className="text-center text-muted-foreground mb-4 max-w-md px-4">
                      Extracting the most prominent and visually important colors in your image...
                    </p>
                  </div>
                )}

                {creditState.credits === 0 && !isAdminMode && (
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg border border-border">
                    <RefreshCw className="h-12 w-12 text-muted-foreground mb-4 animate-spin" />
                    <h3 className="text-lg font-medium mb-2">Credits Depleted</h3>
                    {timeRemaining && (
                      <div className="flex items-center gap-2 text-muted-foreground mb-2">
                        <Clock className="h-4 w-4" />
                        <span>Credits available at {timeRemaining}</span>
                      </div>
                    )}
                    <p className="text-center text-muted-foreground mb-4 max-w-md px-4">
                      Credits automatically refill every {getCreditRegenerationPeriod()}, or you can upgrade now for unlimited access.
                    </p>
                    <div className="flex gap-3">
                      <Button variant="outline" onClick={showCreditStatus}>
                        <RefreshCw className="mr-2 h-4 w-4" /> Check Status
                      </Button>
                      <Button onClick={toggleAdminInput}>
                        <CreditCard className="mr-2 h-4 w-4" /> Upgrade Now
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <ImageUploader
                  handleFileSelect={handleFileSelect}
                  setActiveTab={setActiveTab}
                  fileInputRef={fileInputRef}
                />
              </div>
            )}

            {/* Extracted color palette display */}
            {extractedColors.length > 0 && (
              <div className="mt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">Extracted Color Palette</h2>
                  <div className="text-sm text-muted-foreground">
                    {extractedColors.length} / {MAX_COLORS_LIMIT} colors
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  {extractedColors.map((color, index) => (
                    <Card key={index} className="overflow-hidden border">
                      <div
                        className="h-28"
                        style={{ backgroundColor: color.hex }}
                      />
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{color.name}</p>
                            <p className="text-sm text-muted-foreground">{color.hex}</p>
                          </div>
                          <div className="flex gap-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => copyColor(color.hex)}>
                                  {copiedColor === color.hex ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="left">
                                <p>Copy color</p>
                              </TooltipContent>
                            </Tooltip>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => removeColor(index)}>
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="left">
                                <p>Remove color</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </main>
        </div>

        {/* App footer */}
        <footer className="border-t p-2 text-center text-xs text-muted-foreground">
          Coloriqo - The right color tool
        </footer>

        {/* Code preview dialog */}
        {showCodePreview && (
          <CodePreview
            colors={allColors}
            onClose={() => setShowCodePreview(false)}
          />
        )}

        {/* Admin code input dialog */}
        {showAdminInput && (
          <div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={(e) => {
              // Close dialog when clicking on backdrop (outside the modal)
              if (e.target === e.currentTarget) {
                setShowAdminInput(false);
              }
            }}
          >
            <div className="bg-card border rounded-lg shadow-lg w-96 p-6">
              <h3 className="text-lg font-semibold mb-4">Enter Admin Code</h3>
              <div className="space-y-4">
                <input
                  type="password"
                  value={adminCodeInput}
                  onChange={(e) => setAdminCodeInput(e.target.value)}
                  placeholder="Enter admin code..."
                  className="w-full p-2 border rounded-md bg-background text-foreground"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      verifyAdminCode(adminCodeInput);
                    }
                  }}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdminInput(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => verifyAdminCode(adminCodeInput)}
                  >
                    Submit
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}
