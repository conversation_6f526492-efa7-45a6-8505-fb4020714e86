"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Hand, Trash2, Code, CreditCard, Clock, RefreshCw, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipProvider, Too<PERSON><PERSON>Trigger } from "@/components/ui/tooltip"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader } from "@/components/ui/loader"
import { Color, ExtractionMethod } from "@/types"
import { Separator } from "@/components/ui/separator"
import type { CreditState } from "@/lib/credit-service"

// import { Label } from "@/components/ui/label"

interface ColorToolSidebarProps {
  // State props
  activeTab: string
  setActiveTab: (tab: string) => void
  extractedColors: Color[]
  selectedImage: string | null
  selectedFile: File | null
  extractionMethod: ExtractionMethod
  currentPixelColor: string
  isAdminMode: boolean
  showAdminInput: boolean
  adminCodeInput: string
  creditState: CreditState
  timeRemaining: string
  imageSize: { width: number; height: number } | null
  copiedColor: string | null
  isExtracting: boolean
  extractionError: string | null
  isSidebarCollapsed: boolean
  setIsSidebarCollapsed: (collapsed: boolean) => void

  // Function props
  handleUploadClick: () => void
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleExtractionMethodChange: (method: string) => void
  handleExtract: () => void
  toggleAdminInput: () => void
  getCreditRegenerationPeriod: () => string
  copyColor: (color: string) => void
  removeColor: (index: number) => void
  clearAllColors: () => void
  handleShowCodePreview: () => void

  // Refs
  fileInputRef: React.RefObject<HTMLInputElement>
}

export function ColorToolSidebar({
  activeTab,
  setActiveTab,
  extractedColors,
  selectedImage,
  selectedFile,
  // extractionMethod,
  currentPixelColor,
  isAdminMode,
  // showAdminInput,
  // adminCodeInput,
  creditState,
  timeRemaining,
  imageSize,
  copiedColor,
  isExtracting,
  extractionError,
  isSidebarCollapsed,
  setIsSidebarCollapsed,
  handleUploadClick,
  handleFileChange,
  handleExtractionMethodChange,
  handleExtract,
  toggleAdminInput,
  getCreditRegenerationPeriod,
  copyColor,
  removeColor,
  clearAllColors,
  handleShowCodePreview,
  fileInputRef
}: ColorToolSidebarProps) {

  return (
    <>
      {/* Toggle Button - Floating on border for all screen sizes */}
      <Button
        variant="ghost"
        size="icon"
        className={`
          fixed top-[205px] z-50 h-8 w-8 bg-background border shadow-md hover:bg-accent rounded-full
          ${isSidebarCollapsed ? 'left-0 rounded-l-none md:rounded-full md:left-12' : 'left-[238px] md:left-[238px]'}
          transition-all duration-300 ease-in-out
        `}
        onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
      >
        {/* Desktop: Chevron icons */}
        <div className="hidden md:block">
          {isSidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </div>

        {/* Mobile: Color wheel SVG */}
        <div className="block md:hidden">
          <svg width="20px" height="20px" viewBox="-2.19 -2.19 77.38 77.38" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" fill="currentColor" stroke="currentColor">
            <g id="SVGRepo_bgCarrier" strokeWidth="0">
              <rect x="-2.19" y="-2.19" width="77.38" height="77.38" rx="38.69" fill="transparent" strokeWidth="0"></rect>
            </g>
            <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
              <title>design-and-ux/color-wheel</title>
              <desc>Created with Sketch.</desc>
              <defs></defs>
              <g id="design-and-ux/color-wheel" strokeWidth="0.00073" fill="none" fillRule="evenodd">
                <g id="container" transform="translate(2.000000, 2.000000)" fill="transparent" fillRule="nonzero" stroke="transparent" strokeWidth="0.00073">
                  <rect id="mask" x="-1" y="-1" width="71" height="71" rx="14"></rect>
                </g>
                <g id="color-wheel-(1)" transform="translate(11.000000, 11.000000)" fillRule="nonzero">
                  <path d="M3.4085083,38.2546692 C4.51238248,40.1593094 5.87072754,41.9328232 7.46875765,43.5312423 C12.2854157,48.3475113 18.6888199,51 25.5,51 C32.3115692,51 38.7149735,48.3475113 43.5312423,43.5312423 C45.1296615,41.9328232 46.4876175,40.1593094 47.5914917,38.2546692 C49.814415,34.4193192 51,30.0512924 51,25.5 C51,20.9487076 49.814415,16.5806808 47.5914917,12.7453308 C46.4876175,10.8406906 45.1292725,9.06678777 43.5312423,7.46875765 C38.7149735,2.65248868 32.3115692,0 25.5,0 C18.6888199,0 12.2850265,2.65248868 7.46875765,7.46875765 C5.87033846,9.06678777 4.51199341,10.8406906 3.4085083,12.7453308 C1.18558505,16.5806808 0,20.9490967 0,25.5 C0,30.0509033 1.18558505,34.4193192 3.4085083,38.2546692 Z M18.9923172,25.5 C18.9923172,24.3155823 19.3117676,23.2054825 19.867012,22.2475204 C20.9938431,20.3035812 23.0957565,18.9919281 25.5,18.9919281 C27.9042435,18.9919281 30.0061569,20.3035812 31.1333771,22.2475204 C31.6882324,23.2050934 32.0080719,24.3155823 32.0080719,25.5 C32.0080719,26.6844177 31.6882324,27.7945175 31.1333771,28.7520905 C30.0065461,30.6964188 27.9042435,32.0076828 25.5,32.0076828 C23.0957565,32.0076828 20.9938431,30.6964188 19.8666229,28.7520905 C19.3117676,27.7945175 18.9923172,26.6844177 18.9923172,25.5 Z" id="Shape" fill="#FF8398"></path>
                  <path d="M25.5,41.5036468 L25.5,51 C32.3115692,51 38.7149735,48.3475113 43.5312423,43.5312423 C45.1296615,41.9328232 46.4876175,40.1593094 47.5914917,38.2546692 L39.3616562,33.5029907 C36.5943832,38.2857971 31.4232559,41.5036468 25.5,41.5036468 Z" id="Shape" fill="#54E360"></path>
                  <path d="M41.504036,25.5 C41.504036,28.4155197 40.7238923,31.1489411 39.3616562,33.5029907 L47.5914917,38.2546692 C49.814415,34.4193192 51,30.0512924 51,25.5 C51,20.9487076 49.814415,16.5806808 47.5914917,12.7453308 L39.3616562,17.4966202 C40.7238923,19.8510589 41.504036,22.5844803 41.504036,25.5 Z" id="Shape" fill="#008ADF"></path>
                  <path d="M11.6383438,33.5029907 L3.4085083,38.2546692 C4.51238248,40.1593094 5.87072754,41.9328232 7.46875765,43.5312423 C12.2854157,48.3475113 18.6888199,51 25.5,51 L25.5,41.5036468 C19.5767441,41.5036468 14.4056168,38.2857971 11.6383438,33.5029907 Z" id="Shape" fill="#FFD400"></path>
                  <path d="M39.3616562,17.4966202 L47.5914917,12.7453308 C46.4876175,10.8406906 45.1292725,9.06678777 43.5312423,7.46875765 C38.7149735,2.65248868 32.3115692,0 25.5,0 L25.5,9.49596403 C31.4232559,9.49596403 36.5943832,12.7142029 39.3616562,17.4966202 Z" id="Shape" fill="#0065A3"></path>
                  <path d="M3.4085083,38.2546692 L11.6383438,33.5029907 C10.2761077,31.1489411 9.4963532,28.4155197 9.4963532,25.5 C9.4963532,22.5844803 10.2761077,19.8510589 11.6383438,17.4970093 L3.4085083,12.7453308 C1.18558505,16.5806808 0,20.9490967 0,25.5 C0,30.0509033 1.18558505,34.4193192 3.4085083,38.2546692 Z" id="Shape" fill="#FF9100"></path>
                  <path d="M25.5,9.49596403 L25.5,0 C18.6888199,0 12.2850265,2.65248868 7.46875765,7.46875765 C5.87033846,9.06678777 4.51199341,10.8406906 3.4085083,12.7453308 L11.6383438,17.4970093 C14.4056168,12.7142029 19.5767441,9.49596403 25.5,9.49596403 Z" id="Shape" fill="#FF4949"></path>
                  <path d="M32.0080719,25.5 C32.0080719,26.6844177 31.6882324,27.7945175 31.1333771,28.7520905 L39.3616562,33.5029907 C40.7238923,31.1489411 41.504036,28.4155197 41.504036,25.5 C41.504036,22.5840911 40.7238923,19.8510589 39.3616562,17.4966202 L31.1333771,22.2475204 C31.6882324,23.2050934 32.0080719,24.3155823 32.0080719,25.5 Z" id="Shape" fill="#0065A3"></path>
                  <path d="M31.1333771,22.2475204 L39.3616562,17.4970093 C36.5943832,12.7142029 31.4232559,9.49596403 25.5,9.49596403 L25.5,18.9919281 C27.9042435,18.9919281 30.0061569,20.3035812 31.1333771,22.2475204 Z" id="Shape" fill="#005183"></path>
                  <path d="M25.5,32.0076828 L25.5,41.5036468 C31.4232559,41.5036468 36.5943832,38.2857971 39.3616562,33.5029907 L31.1333771,28.7524796 C30.0065461,30.6964188 27.9042435,32.0076828 25.5,32.0076828 Z" id="Shape" fill="#00AB5E"></path>
                  <path d="M25.5,41.5036468 L25.5,32.0076828 C23.0957565,32.0076828 20.9938431,30.6964188 19.8666229,28.7520905 L11.6383438,33.5029907 C14.4056168,38.2857971 19.5767441,41.5036468 25.5,41.5036468 Z" id="Shape" fill="#FF9F04"></path>
                  <path d="M18.9923172,25.5 C18.9923172,24.3155823 19.3117676,23.2054825 19.867012,22.2475204 L11.6383438,17.4970093 C10.2761077,19.8510589 9.4963532,22.5844803 9.4963532,25.5 C9.4963532,28.4155197 10.2761077,31.1489411 11.6383438,33.5029907 L19.8666229,28.7520905 C19.3117676,27.7945175 18.9923172,26.6844177 18.9923172,25.5 Z" id="Shape" fill="#FF4B00"></path>
                  <path d="M25.5,18.9919281 L25.5,9.49596403 C19.5767441,9.49596403 14.4056168,12.7142029 11.6383438,17.4970093 L19.867012,22.2475204 C20.9938431,20.3035812 23.0957565,18.9919281 25.5,18.9919281 Z" id="Shape" fill="#E80048"></path>
                </g>
              </g>
            </g>
          </svg>
        </div>
      </Button>

      {/* Sidebar */}
      <aside className={`
        ${isSidebarCollapsed ? 'w-0 md:w-16' : 'w-64'} 
        ${isSidebarCollapsed ? 'md:border-r' : 'border-r'} 
        bg-muted/40 flex flex-col overflow-hidden transition-all duration-300 ease-in-out
        relative
        ${isSidebarCollapsed && 'md:bg-muted/40'}
      `}>

        <div className={`${isSidebarCollapsed ? 'p-2' : 'p-4'} flex flex-col overflow-y-auto h-full transition-all duration-300`}>
          {!isSidebarCollapsed ? (
            <Tabs defaultValue="upload" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="w-full">
                <TabsTrigger value="upload" className="flex-1">Upload</TabsTrigger>
                <TabsTrigger value="palette" className="flex-1">Palette</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="mt-4">
                <div className="space-y-4">
                  <Button onClick={handleUploadClick} variant="default" className="w-full">
                    <Upload className="mr-2 h-4 w-4" /> Upload Image
                  </Button>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="image/jpeg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml,image/x-icon,image/vnd.microsoft.icon,image/heic,image/heif,image/avif,image/jp2,image/jpx,image/jpm,image/jxl"
                    className="hidden"
                  />

                  {selectedImage && (
                    <div className="space-y-4">
                      <Separator />
                      <p className="text-sm font-medium">Extraction Method</p>
                      <Tabs defaultValue="ai" className="w-full" onValueChange={(value) => handleExtractionMethodChange(value)}>
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="ai" className="flex items-center">
                            <Cpu className="mr-2 h-4 w-4" /> AI
                          </TabsTrigger>
                          <TabsTrigger value="manual" className="flex items-center">
                            <Hand className="mr-2 h-4 w-4" /> Manual
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="ai" className="mt-2">
                          <p className="text-xs text-muted-foreground mb-2">Extract multiple colors at once using AI.</p>
                          {creditState.credits === 0 && !isAdminMode ? (
                            <div className="p-3 border border-dashed rounded-md bg-muted/50">
                              <div className="flex flex-col items-center mb-3">
                                <RefreshCw className="h-5 w-5 text-muted-foreground mb-2 animate-spin" />
                                <p className="text-sm text-center mb-1">Out of credits</p>
                                {timeRemaining && (
                                  <p className="text-xs text-muted-foreground flex items-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    Credits at {timeRemaining}
                                  </p>
                                )}
                              </div>
                              <div className="flex justify-between items-center gap-2">
                                <p className="text-xs text-muted-foreground">Credits refill every {getCreditRegenerationPeriod()}</p>
                                <Button
                                  onClick={toggleAdminInput}
                                  size="sm"
                                  variant="outline"
                                  className="whitespace-nowrap"
                                >
                                  <CreditCard className="mr-2 h-3 w-3" /> Upgrade
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <Button
                              onClick={handleExtract}
                              disabled={isExtracting || !selectedFile}
                              className="w-full"
                              size="sm"
                            >
                              {isExtracting ? (
                                <>
                                  <Loader size="sm" className="mr-2" /> Extracting...
                                </>
                              ) : (
                                <>
                                  <Cpu className="mr-2 h-4 w-4" /> Extract Colors
                                </>
                              )}
                            </Button>
                          )}
                          {extractionError && (
                            <p className="text-xs text-red-500 mt-2">{extractionError}</p>
                          )}
                        </TabsContent>

                        <TabsContent value="manual" className="mt-2">
                          <div className="space-y-3">
                            {creditState.credits === 0 && !isAdminMode ? (
                              <div className="p-3 border border-dashed rounded-md bg-muted/50">
                                <div className="flex flex-col items-center mb-3">
                                  <RefreshCw className="h-5 w-5 text-muted-foreground mb-2 animate-spin" />
                                  <p className="text-sm text-center mb-1">Out of credits</p>
                                  {timeRemaining && (
                                    <p className="text-xs text-muted-foreground flex items-center">
                                      <Clock className="h-3 w-3 mr-1" />
                                      Credits at {timeRemaining}
                                    </p>
                                  )}
                                </div>
                                <div className="flex justify-between items-center gap-2">
                                  <p className="text-xs text-muted-foreground">Credits refill every {getCreditRegenerationPeriod()}</p>
                                  <Button
                                    onClick={toggleAdminInput}
                                    size="sm"
                                    variant="outline"
                                    className="whitespace-nowrap"
                                  >
                                    <CreditCard className="mr-2 h-3 w-3" /> Upgrade
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="flex items-center">
                                  <div className={`w-3 h-3 rounded-full mr-2 bg-green-500`}></div>
                                  <p className="text-xs font-medium">Ready to pick colors</p>
                                </div>
                                <p className="text-xs text-muted-foreground">Hover over the image to see a magnified view, then click to extract any color you want.</p>
                                {currentPixelColor && (
                                  <div className="p-2 border rounded-md bg-card">
                                    <p className="text-xs mb-1 text-muted-foreground">Current color:</p>
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className="w-8 h-8 rounded-md border"
                                        style={{ backgroundColor: currentPixelColor }}
                                      />
                                      <span className="text-xs font-mono">{currentPixelColor}</span>
                                    </div>
                                  </div>
                                )}
                                <div className="border-t pt-2 mt-2">
                                  <p className="text-[10px] text-muted-foreground">
                                    <span className="font-medium">Tip:</span> You can pick multiple colors without having to reselect this tab each time.
                                  </p>
                                </div>
                              </>
                            )}
                          </div>
                        </TabsContent>
                      </Tabs>

                      {imageSize && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground">
                            Image size: {Math.round(imageSize.width)} × {Math.round(imageSize.height)}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="palette" className="mt-4">
                <div className="space-y-4">
                  {extractedColors.length > 0 ? (
                    <>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Extracted Colors</p>
                        <div className="flex items-center gap-2">
                          {!isAdminMode && (
                            <span className={`text-xs ${extractedColors.length >= 45 ? 'text-orange-500 font-medium' : 'text-muted-foreground'}`}>
                              {extractedColors.length}/50
                            </span>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={clearAllColors}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {extractedColors.length >= 45 && extractedColors.length < 50 && (
                        <div className="p-2 bg-orange-50 border border-orange-200 rounded-md">
                          <p className="text-xs text-orange-700">
                            Approaching limit: {50 - extractedColors.length} colors remaining
                          </p>
                        </div>
                      )}

                      <div className="space-y-2 max-h-[500px] overflow-y-auto pr-1">
                        {extractedColors.map((color, index) => (
                          <div key={index} className="flex items-center group">
                            <div
                              className="h-8 w-8 rounded-md mr-2 border"
                              style={{ backgroundColor: color.hex }}
                            />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{color.name}</p>
                              <p className="text-xs text-muted-foreground">{color.hex}</p>
                            </div>
                            <div className="flex">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => copyColor(color.hex)}
                              >
                                {copiedColor === color.hex ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeColor(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={handleShowCodePreview}
                        >
                          <Code className="mr-2 h-4 w-4" /> View Code
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 text-center text-muted-foreground">
                      <Pipette className="h-12 w-12 mb-4 opacity-20" />
                      <p className="mb-2">No colors extracted yet</p>
                      <p className="text-xs">Upload an image and use AI or the color picker to extract colors</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            // Collapsed sidebar - show icon buttons
            <div className="hidden md:flex flex-col items-center space-y-4 pt-8">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={activeTab === 'upload' ? 'default' : 'ghost'}
                      size="icon"
                      onClick={() => {
                        setActiveTab('upload')
                        setIsSidebarCollapsed(false)
                      }}
                    >
                      <Upload className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Upload</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={activeTab === 'palette' ? 'default' : 'ghost'}
                      size="icon"
                      onClick={() => {
                        setActiveTab('palette')
                        setIsSidebarCollapsed(false)
                      }}
                    >
                      <Pipette className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Palette</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
      </aside>


    </>
  )
}