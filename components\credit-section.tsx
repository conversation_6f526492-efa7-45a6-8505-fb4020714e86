import  Link  from "next/link";
import React from "react";

const credits = [
  {
    title: "<PERSON><PERSON><PERSON>",
    name: "<PERSON>ont Velvetyne",
    url: "https://velvetyne.fr/fonts/pilowlava/",
  },
  {
    title: "Icons",
    name: "SVG Repo",
    url: "https://www.svgrepo.com/collections/icon/",
    className: "border-t-2 border-t-black dark:border-t-white w-[230px]",
  },
];

export default function CreditsSection() {
  return (
    <section className="min-h-screen flex flex-col items-center justify-center p-6 -mt-12">
      <h1 className="text-4xl md:text-5xl font-pilowlava font-bold text-gray-800 dark:text-white mb-6 animate-fade-in">
        A Splash of Credits
      </h1>
      <p className="text-lg text-gray-700 dark:text-white max-w-xl text-center mb-12">
        Every great palette is made of many colors and so is every great app.
        Here are the amazing resources who made this project unique and
        vibrant.
      </p>
      <div className="flex flex-col justify-center items-center w-full ">
        {credits.map((credit) => (
          <div
            key={credit.name}
            className={`p-6 duration-300 ${credit.className || ""}`}
          >
            <h2 className="flex flex-col leading-8 text-xl text-center font-semibold">
              {credit.title}
              <Link
                href={credit.url}
                target="_blank"
                rel="noopener noreferrer"
                className="font-normal"
              >
                {credit.name}
              </Link>
            </h2>
          </div>
        ))}
      </div>
      <p className="mt-12 text-gray-600 dark:text-white text-center mx-auto max-w-2xl">
        From pixels to palettes, you&apos;ve all helped paint this masterpiece.
      </p>
    </section>
  );
}
