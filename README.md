# Coloriqo

A SaaS application that allows users to extract colors from images using AI (Google Gemini API) or manually with a color picker.

## Features

- Upload images and extract colors
- Extract colors using AI with Google Gemini 1.5 Flash API
- Manual color picking with eye-dropper tool
- Copy colors to clipboard
- Export color palette

## Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```
3. Create a `.env.local` file in the root directory with your keys:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   CREDIT_SECRET_KEY=your_strong_random_secret_key_here
   NEXT_PUBLIC_ADMIN_CODE=your_admin_access_code_here
   ```
   
   You can get a Gemini API key from [Google AI Studio](https://makersuite.google.com/). Make sure your API key has access to the Gemini 1.5 Flash model.

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Environment Variables

- `GEMINI_API_KEY`: Google Gemini API key for AI-based color extraction
- `NEXT_PUBLIC_CREDIT_SECRET_KEY`: Secret key for securing the credit system
- `NEXT_PUBLIC_ADMIN_CODE`: Admin access code (note: this will be exposed in client-side code)

## Technologies

- Next.js
- React
- TypeScript
- Tailwind CSS
- shadcn/ui
- Google Gemini 1.5 Flash API 