// components/HashScroller.tsx
"use client";
import { useEffect } from "react";

export default function HashScroller() {
  useEffect(() => {
    const scrollToHash = () => {
      const id = window.location.hash?.slice(1);
      if (!id) return;
      const el = document.getElementById(id);
      if (!el) return;
      // Wait a frame so layout is ready, then smooth scroll
      requestAnimationFrame(() => {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
      });
    };

    scrollToHash(); // on first mount (when arriving from /#id)
    window.addEventListener("hashchange", scrollToHash);
    return () => window.removeEventListener("hashchange", scrollToHash);
  }, []);

  return null;
}
