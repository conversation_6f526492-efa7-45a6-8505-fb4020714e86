'use client'
import React from 'react';
import Header from '@/components/header'
import Footer from '@/components/footer'
import whiteMonkey from '@/public/images/white-monkey.png'
import blackMonkey from '@/public/images/black-monkey.png'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import FloatingDockDemo from '@/components/share';

const PricingPage = () => {
    const { theme } = useTheme()
  
  return (
    <>
      <div className="h-1.5 w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500" />
      <Header />
      <div className="pricing-page min-h-screen flex flex-col items-center justify-start px-4 py-8">
        <h1 className="text-5xl font-pilowlava  text-black dark:text-[#fafafa] mt-6">PRICING</h1>
         <p className=" text-center text-lg mt-4 max-w-lg md:max-w-2xl text-black dark:text-[#fafafa] px-2">
          Payments coming soon! Until then, settle your bill by sharing our service with your friends.
        </p>
        <FloatingDockDemo/>        
        <Image
          src={theme === "light" ? blackMonkey : whiteMonkey }
          alt="Pricing Banner"
          width={800}
          height={800} 
          className="mx-auto mt-32 w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl h-auto object-contain" 
        />
        
      </div>
     <Footer />
    
    </>
  )
}

export default PricingPage