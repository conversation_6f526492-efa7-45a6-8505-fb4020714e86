import React from 'react'
import Link from "next/link";
import { LayoutPanelTop, } from "lucide-react";

const Footer = () => {
  return (
    <footer className="pt-6 pb-2 border-t border-neutral-200 dark:border-neutral-800 ">
    <div className="container mx-auto px-4 md:px-8">
      <div className="grid grid-cols-2 mb-6 md:grid-cols-6 xl:grid-cols-8 gap-8 mt-3">
        <div className="col-span-2 md:col-span-3 xl:col-span-5">
          <div className="flex items-center gap-2 ">
            <div className="relative">
              <LayoutPanelTop className="h-6 w-6 text-transparent" />
              <div
                className="absolute inset-0 h-6 w-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-md"
                style={{
                  maskImage:
                    "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 3v3m0 12v3M5.636 5.636l2.122 2.122m8.485 8.485 2.121 2.121M3 12h3m12 0h3M5.636 18.364l2.122-2.122m8.485-8.485 2.121-2.121'/%3E%3C/svg%3E\")",
                  maskSize: "cover",
                }}
              />
            </div>
            <h1
              className="text-xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-400 dark:to-indigo-400"
              style={{
                fontFamily: "var(--font-montserrat)",
                letterSpacing: "-0.5px",
                fontWeight: "800",
              }}
            >
              <Link href="/tool" data-barba="wrapper">
                Coloriqo
              </Link>
              
            </h1>
          </div>
          <p className="text-sm text-muted-foreground mt-2 md:max-w-60 lg:max-w-full">
            AI-powered color extraction for perfect palettes.
          </p>
            <div className="flex space-x-6 mt-3 md:mt-6 lg:py-0 ">
          <Link
            href="https://www.instagram.com/wewiselabs"
            target="_blank"
            rel="noopener noreferrer"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              x="0px"
              y="0px"
              width="20"
              height="20"
              fill="currentColor"
              viewBox="0 0 50 50"
            >
              <path d="M 16 3 C 8.83 3 3 8.83 3 16 L 3 34 C 3 41.17 8.83 47 16 47 L 34 47 C 41.17 47 47 41.17 47 34 L 47 16 C 47 8.83 41.17 3 34 3 L 16 3 z M 37 11 C 38.1 11 39 11.9 39 13 C 39 14.1 38.1 15 37 15 C 35.9 15 35 14.1 35 13 C 35 11.9 35.9 11 37 11 z M 25 14 C 31.07 14 36 18.93 36 25 C 36 31.07 31.07 36 25 36 C 18.93 36 14 31.07 14 25 C 14 18.93 18.93 14 25 14 z M 25 16 C 20.04 16 16 20.04 16 25 C 16 29.96 20.04 34 25 34 C 29.96 34 34 29.96 34 25 C 34 20.04 29.96 16 25 16 z"></path>
            </svg>
          </Link>
          <Link
            href="https://github.com/wewiselabs"
            target="_blank"
            rel="noopener noreferrer"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              fill="currentColor"
              viewBox="0 0 16 16"
            >
              <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8" />
            </svg>
          </Link>
          <Link
            href="https://www.linkedin.com/company/wewise-labs/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              x="0px"
              y="0px"
              width="20"
              height="20"
              fill="currentColor"
              viewBox="0 0 50 50"
            >
              <path d="M41,4H9C6.24,4,4,6.24,4,9v32c0,2.76,2.24,5,5,5h32c2.76,0,5-2.24,5-5V9C46,6.24,43.76,4,41,4z M17,20v19h-6V20H17z M11,14.47c0-1.4,1.2-2.47,3-2.47s2.93,1.07,3,2.47c0,1.4-1.12,2.53-3,2.53C12.2,17,11,15.87,11,14.47z M39,39h-6c0,0,0-9.26,0-10 c0-2-1-4-3.5-4.04h-0.08C27,24.96,26,27.02,26,29c0,0.91,0,10,0,10h-6V20h6v2.56c0,0,1.93-2.56,5.81-2.56 c3.97,0,7.19,2.73,7.19,8.26V39z"></path>
            </svg>
          </Link>
          <Link
            href="https://twitter.com/wewiselabs"
            target="_blank"
            rel="noopener noreferrer"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              x="0px"
              y="0px"
              width="20"
              height="20"
              fill="currentColor"
              viewBox="0 0 50 50"
            >
              <path d="M 11 4 C 7.134 4 4 7.134 4 11 L 4 39 C 4 42.866 7.134 46 11 46 L 39 46 C 42.866 46 46 42.866 46 39 L 46 11 C 46 7.134 42.866 4 39 4 L 11 4 z M 13.085938 13 L 21.023438 13 L 26.660156 21.009766 L 33.5 13 L 36 13 L 27.789062 22.613281 L 37.914062 37 L 29.978516 37 L 23.4375 27.707031 L 15.5 37 L 13 37 L 22.308594 26.103516 L 13.085938 13 z M 16.914062 15 L 31.021484 35 L 34.085938 35 L 19.978516 15 L 16.914062 15 z"></path>
            </svg>
          </Link>
        </div>
        </div>

        <div className=''>
          <h4 className="font-medium mb-3">Product</h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link
                href="/#features"
                className="hover:text-foreground transition-colors"
              >
                Features
              </Link>
            </li>
            <li>
              <Link
                href="/pricing"
                className="hover:text-foreground transition-colors"
              >
                Pricing
              </Link>
            </li>
             <li className='block md:hidden'>
              <Link
                href="/#video"
                className="hover:text-foreground transition-colors"
              >
                Tutorial
              </Link>
            </li>
          </ul>
        </div>

         <div className=''>
          <h4 className="font-medium mb-3">Resources</h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link
                href="/#video"
                className="hover:text-foreground transition-colors"
              >
                Tutorial
              </Link>
            </li>
            <li>
              <Link
                href="/credits"
                className="hover:text-foreground transition-colors"
              >
                Credits
              </Link>
            </li>
          </ul>
          
        </div> 

        <div className=''>
          <h4 className="font-medium mb-3">Company</h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li>
              <Link
                href="https://www.wewiselabs.com/about"
                className="hover:text-foreground transition-colors"
              >
                About us
              </Link>
            </li>
            <li>
              <Link
                href="/#contact"
                className="hover:text-foreground transition-colors"
              >
                Contact
              </Link>
            </li>
            <li>
              <Link
                href="/privacy-page"
                className="hover:text-foreground transition-colors"
              >
               Terms &amp; Privacy
              </Link>
            </li>
          </ul>
        </div>
      </div>

      <div className="mt-10 pt-2 border-t border-neutral-200 dark:border-neutral-800 flex flex-col md:flex-row justify-between items-center">
        <p className="text-xs text-muted-foreground">
          © {new Date().getFullYear()} Coloriqo. All rights reserved.
        </p>
        <div className="flex flex-row gap-2 ">


                <p className="mt-1 sm:mb-0 text-xs text-muted-foreground">Powered By</p>
                <p> <svg
                  width="25"
                  height="25"
                  viewBox="0 0 180 128"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="transition-colors duration-200"
                >
                  <path d="M78.6259 38.832C73.1355 30.0558 79.9798 19.0256 90.9159 19.0256V19.0256C95.9638 19.0256 100.622 21.5226 103.145 25.5801L143.724 90.8642C145.832 94.2543 146.186 98.3492 144.69 102.018V102.018C140.494 112.31 125.342 113.504 119.402 104.01L78.6259 38.832Z" className="fill-black dark:fill-white" />
                  <path d="M34.6428 42.8981C27.6461 32.2271 35.8741 18.6466 49.2631 18.7671V18.7671C55.3185 18.8216 60.9183 21.8481 63.989 26.726L103.195 89.0068C106.026 93.5035 106.218 99.0116 103.705 103.629V103.629C97.9155 114.268 81.7676 114.769 75.0466 104.519L34.6428 42.8981Z" className="fill-black dark:fill-white" />
                  <path d="M160.053 69.3467C161.229 66.3076 160.897 62.9169 159.148 60.0817L137.86 25.5824C135.668 22.0288 131.606 19.8274 127.243 19.8274V19.8274C117.98 19.8274 112.348 29.176 117.096 36.671L138.808 70.9438C143.918 79.0109 156.686 78.0511 160.053 69.3467V69.3467Z" className="fill-black dark:fill-white" />
                  <path d="M163.801 32.4324C166.824 32.4067 169.63 33.7805 171.302 36.1052L171.971 37.0345C173.82 39.6042 174.018 42.9461 172.49 45.7714V45.7714C169.105 52.0318 159.729 52.3625 156.178 46.3466L155.599 45.3649C152.226 39.6513 156.767 32.4922 163.801 32.4324V32.4324Z" className="fill-black dark:fill-white" />
                </svg></p> 
                <p className="mt-1 text-xs text-muted-foreground "><Link href="https://www.wewiselabs.com" target="_blank" >Wewise Labs</Link></p>
              </div>

      
      </div>
    </div>
  </footer>
  )
}

export default Footer
