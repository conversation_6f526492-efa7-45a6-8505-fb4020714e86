@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap");

@import url('https://fonts.cdnfonts.com/css/pilowlava');

@tailwind base;
@tailwind components;
@tailwind utilities; 

/* Hard block horizontal scroll globally */
html,
body {
  overflow-x: hidden !important;
  overscroll-behavior-x: none !important;
}

body {
  font-family: var(--font-montserrat), sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-rgb: 23, 23, 23;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-rgb: 250, 250, 250;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* ========= REVAMPED PAGE TRANSITIONS ========= */
/* Transition overlay - optimized for performance */
.transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;
  pointer-events: none;
  overflow: hidden;
  will-change: transform, opacity;
  transform: translateZ(0);
  background-color: transparent;
}

/* Main background with seamless gradient */
.transition-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #352cb0 0%, #6355e4 50%, #9f93ff 100%);
  will-change: clip-path;
  transform: translateZ(0);
  clip-path: circle(0% at center);
}

/* SVG container for wave animations */
.transition-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  overflow: visible;
  pointer-events: none;
}

/* SVG Path styling for the wave effects */
.transition-path {
  stroke-dasharray: 100%;
  stroke-dashoffset: 100%;
  will-change: stroke-dashoffset, opacity;
  transform: translateZ(0);
}

/* Path color variations */
.path-0 {
  stroke: rgba(255, 255, 255, 0.7);
  stroke-width: 0.6;
}

.path-1 {
  stroke: rgba(223, 239, 255, 0.5);
  stroke-width: 0.4;
}

.path-2 {
  stroke: rgba(205, 179, 255, 0.6);
  stroke-width: 0.5;
}

.path-3 {
  stroke: rgba(255, 252, 238, 0.5);
  stroke-width: 0.3;
}

.path-4 {
  stroke: rgba(230, 220, 255, 0.6);
  stroke-width: 0.4;
}

.path-5 {
  stroke: rgba(255, 255, 255, 0.7);
  stroke-width: 0.5;
}

/* Text container styling */
.transition-text {
  position: relative;
  z-index: 5;
  text-align: center;
  transform: translateZ(0);
  will-change: transform, opacity;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

/* Brand text styling */
.transition-brand {
  font-family: "Playfair Display", serif;
  font-weight: 700;
  font-size: 5rem;
  color: white;
  margin-bottom: 1rem;
  letter-spacing: 0.03em;
  line-height: 1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  will-change: transform, opacity;
  transform: translateZ(0);
  background: linear-gradient(to right, #ffffff, #e0e0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Phrase styling */
.transition-phrase {
  font-family: "Inter", sans-serif;
  font-weight: 300;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.12em;
  will-change: transform, opacity;
  transform: translateZ(0);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

/* Page content container */
.page-content {
  min-height: 100vh;
  width: 100%;
  position: relative;
  will-change: opacity;
  transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-background,
  .transition-path,
  .transition-brand,
  .transition-phrase {
    transition-duration: 0.1s !important;
    animation-duration: 0.1s !important;
  }
}

/* Scroll To Top Button Styles */
@keyframes float {
  0% {
    transform: translateY(0px);
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(-5px);
    box-shadow: 0 15px 20px 0px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(0px);
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.2);
  }
}

.scroll-to-top-btn {
  animation: float 3s ease-in-out infinite;
}

.scroll-to-top-btn:hover {
  transform: scale(1.1);
  transition: transform 0.3s ease;
  animation-play-state: paused;
}

.scroll-to-top-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

@font-face {
  font-family: "Rubik Doodle Shadow";
  src: url("/fonts/rubik-doodle-shadow/RubikDoodleShadow-Regular.ttf")
    format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Pilowlava';
  src: url('/fonts/Pilowlava-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
