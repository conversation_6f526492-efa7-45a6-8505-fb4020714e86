/**
 * Note: Use position fixed according to your needs
 * Desktop navbar is better positioned at the bottom
 * Mobile navbar is better positioned at bottom right.
 **/

import { cn } from "@/lib/utils";
import { Share2 } from 'lucide-react';
import {
  AnimatePresence,
  motion,
} from "motion/react";

import {useState } from "react";

export type FloatingDockItem =
  | { title: string; icon: React.ReactNode; href: string; onClick?: undefined }
  | { title: string; icon: React.ReactNode; onClick: () => void | Promise<void>; href?: undefined };

export const FloatingDock = ({
  items,
  mobileClassName,
}: {
  items: FloatingDockItem[];
  mobileClassName?: string;
}) => {
  return (
    <>
      <FloatingDockMobile items={items} className={mobileClassName} />
    </>
  );
};

const FloatingDockMobile = ({
  items,
  className,
}: {
  items: FloatingDockItem[];
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  return (
    <div className={cn("relative", className)}>
      <AnimatePresence>
        {open && (
          <motion.div
            layoutId="nav"
            className="absolute inset-x-0 bottom-full flex justify-center items-center gap-2"
          >
            {items.map((item, idx) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: 1,
                  y: 0,
                }}
                exit={{
                  opacity: 0,
                  y: 10,
                  transition: {
                    delay: idx * 0.05,
                  },
                }}
                transition={{ delay: (items.length - 1 - idx) * 0.05 }}
              >
                {item.href ? (
                  <a
                    href={item.href}
                    className="flex h-14 w-14 items-center justify-center rounded-full bg-gray-50 dark:bg-neutral-900"
                  >
                    <div className="h-6 w-6">{item.icon}</div>
                  </a>
                ) : (
                  <button
                    type="button"
                    onClick={item.onClick}
                    className="flex h-14 w-14 items-center justify-center rounded-full bg-gray-50 dark:bg-neutral-900"
                  >
                    <div className="h-6 w-6">{item.icon}</div>
                  </button>
                )}
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
      <button
        onClick={() => setOpen(!open)}
        className="flex h-14 w-14 items-center justify-center rounded-full bg-gray-50 dark:bg-neutral-800"
      >
        <Share2 className="h-6 w-6 text-neutral-900 dark:text-white" />
      </button>
    </div>
  );
};
