"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ButtonProps } from "@/components/ui/button";

interface GradientButtonProps extends ButtonProps {
  gradient?: "default" | "coloriqo";
  className?: string;
  children: React.ReactNode;
}

export function GradientButton({ 
  gradient = "coloriqo", 
  className, 
  children, 
  ...props 
}: GradientButtonProps) {

  return (
    <Button
      className={cn(
        "relative rounded-full border-0 overflow-hidden font-medium", 
        className
      )}
      style={{ 
        background: gradient === "coloriqo" 
          ? "linear-gradient(90deg, #22c55e, #a855f7, #ef4444)"
          : "linear-gradient(90deg, #3b82f6, #4f46e5)", 
        backgroundSize: "200% 100%",
        animation: "gradientX 15s ease infinite"
      }}
      {...props}
    >
      <span className="relative z-10">{children}</span>
    </Button>
  );
} 