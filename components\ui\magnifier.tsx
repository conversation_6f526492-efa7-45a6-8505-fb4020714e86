import React, { useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';

interface MagnifierProps {
  sourceCanvas: HTMLCanvasElement | null;
  x: number;
  y: number;
  zoomLevel: number;
  size: number;
}

export function Magnifier({ sourceCanvas, x, y, zoomLevel = 4, size = 150 }: MagnifierProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { theme } = useTheme();

  useEffect(() => {
    if (!sourceCanvas || !canvasRef.current) return;

    const magnifierCanvas = canvasRef.current;
    const magnifierCtx = magnifierCanvas.getContext('2d', { alpha: false });
    if (!magnifierCtx) return;

    // Set the magnifier canvas dimensions
    magnifierCanvas.width = size;
    magnifierCanvas.height = size;

    // Calculate source rectangle (area to zoom in on)
    const sourceSize = Math.ceil(size / zoomLevel);
    const halfSourceSize = Math.floor(sourceSize / 2);
    
    // Source coordinates (centered around cursor)
    const sourceX = Math.max(0, Math.min(sourceCanvas.width - sourceSize, x - halfSourceSize));
    const sourceY = Math.max(0, Math.min(sourceCanvas.height - sourceSize, y - halfSourceSize));

    // Clear previous content with theme-aware background
    const bgColor = theme === 'dark' ? '#1e1e1e' : '#f8f9fa';
    magnifierCtx.fillStyle = bgColor;
    magnifierCtx.fillRect(0, 0, size, size);
    
    // Draw the zoomed image
    try {
      magnifierCtx.drawImage(
        sourceCanvas,
        sourceX, sourceY, sourceSize, sourceSize,
        0, 0, size, size
      );
      
      // Draw crosshair
      const centerX = size / 2;
      const centerY = size / 2;
      
      // Draw crosshair lines - use lighter color for dark mode and darker for light mode
      const lineColor = theme === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)';
      magnifierCtx.strokeStyle = lineColor;
      magnifierCtx.lineWidth = 1;
      
      // Horizontal line
      magnifierCtx.beginPath();
      magnifierCtx.moveTo(0, centerY);
      magnifierCtx.lineTo(size, centerY);
      magnifierCtx.stroke();
      
      // Vertical line
      magnifierCtx.beginPath();
      magnifierCtx.moveTo(centerX, 0);
      magnifierCtx.lineTo(centerX, size);
      magnifierCtx.stroke();
      
      // Draw a small circle at the center
      magnifierCtx.beginPath();
      magnifierCtx.arc(centerX, centerY, 4, 0, Math.PI * 2);
      
      // Circle colors adapt to theme
      const circleOuterColor = theme === 'dark' ? 'black' : 'white';
      const circleInnerColor = theme === 'dark' ? 'white' : 'black';
      
      magnifierCtx.strokeStyle = circleOuterColor;
      magnifierCtx.lineWidth = 2;
      magnifierCtx.stroke();
      magnifierCtx.strokeStyle = circleInnerColor;
      magnifierCtx.lineWidth = 1;
      magnifierCtx.stroke();
    } catch (error) {
      console.error('Error drawing magnifier:', error);
    }
  }, [sourceCanvas, x, y, zoomLevel, size, theme]);

  return (
    <div className="absolute p-1 bg-card border shadow-lg rounded-full overflow-hidden">
      <canvas 
        ref={canvasRef} 
        width={size} 
        height={size} 
        className="rounded-full"
      />
    </div>
  );
} 