declare module '@barba/core' {
  interface BarbaData {
    current: {
      container: HTMLElement;
      namespace?: string;
      url: {
        hash: string;
        href: string;
        path: string;
        port: string | null;
        query: object;
      };
    };
    next: {
      container: HTMLElement;
      namespace?: string;
      url: {
        hash: string;
        href: string;
        path: string;
        port: string | null;
        query: object;
      };
    };
    trigger?: HTMLElement;
  }

  interface BarbaOptions {
    transitions?: Array<{
      name?: string;
      to?: string | RegExp | string[] | RegExp[];
      from?: string | RegExp | string[] | RegExp[];
      leave?: (data: BarbaData) => Promise<void> | void;
      enter?: (data: BarbaData) => Promise<void> | void;
      beforeEnter?: (data: BarbaData) => Promise<void> | void;
      afterEnter?: (data: BarbaData) => Promise<void> | void;
      beforeLeave?: (data: BarbaData) => Promise<void> | void;
      afterLeave?: (data: BarbaData) => Promise<void> | void;
    }>;
    views?: Array<{
      namespace: string;
      beforeEnter?: (data: BarbaData) => Promise<void> | void;
      afterEnter?: (data: BarbaData) => Promise<void> | void;
      beforeLeave?: (data: BarbaData) => Promise<void> | void;
      afterLeave?: (data: BarbaData) => Promise<void> | void;
    }>;
    preventRunning?: boolean;
    timeout?: number;
    prefetchIgnore?: boolean;
    schema?: {
      wrapper?: string;
      container?: string;
      namespace?: string;
    };
  }

  export function init(options?: BarbaOptions): void;
  
  export default {
    init
  };
} 