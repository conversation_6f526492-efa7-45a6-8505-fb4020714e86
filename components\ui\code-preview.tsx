import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Co<PERSON>, Check } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Color } from "@/types";
import { Highlight, themes } from "prism-react-renderer";
import { useTheme } from "next-themes";

interface CodePreviewProps {
  colors: Color[];
  onClose: () => void;
}

export function CodePreview({ colors, onClose }: CodePreviewProps) {
  const [activeTab, setActiveTab] = useState<string>("css");
  const [copiedFormat, setCopiedFormat] = useState<string | null>(null);
  const { theme } = useTheme();

  // Format as CSS variables
  const cssVariables = colors
    .map((color) => `--color-${color.name.toLowerCase().replace(/\s+/g, "-")}: ${color.hex};`)
    .join("\n");

  // Format as JSON
  const jsonFormat = JSON.stringify(colors, null, 2);

  // Format as Tailwind config
  const tailwindFormat = `{
  theme: {
    extend: {
      colors: {
${colors.map((color) => `        '${color.name.toLowerCase().replace(/\s+/g, "-")}': '${color.hex}',`).join("\n")}
      }
    }
  }
}`;

  const formats = {
    css: {
      code: `/* CSS Variables */
:root {
${cssVariables}
}`,
      language: "css"
    },
    json: {
      code: jsonFormat,
      language: "json"
    },
    tailwind: {
      code: `/* Tailwind Config */
${tailwindFormat}`,
      language: "javascript"
    }
  };

  // Select appropriate syntax theme based on current app theme
  const syntaxTheme = theme === "dark" ? themes.vsDark : themes.vsLight;

  const copyCode = (format: string) => {
    try {
      const codeToCopy = formats[format as keyof typeof formats].code;
      
      navigator.clipboard.writeText(codeToCopy).then(() => {
        setCopiedFormat(format);
        toast({
          title: "Code copied!",
          description: `${format.toUpperCase()} format has been copied to clipboard.`,
          duration: 2000,
        });

        // Reset copied state after 2 seconds
        setTimeout(() => {
          setCopiedFormat(null);
        }, 2000);
      }).catch(error => {
        console.error("Failed to copy:", error);
        toast({
          title: "Failed to copy",
          description: "Could not copy to clipboard. Try again manually.",
          variant: "destructive"
        });
      });
    } catch (error) {
      console.error("Clipboard error:", error);
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard. Try again manually.",
        variant: "destructive"
      });
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={(e) => {
        // Close when clicking on backdrop (outside the modal)
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-card border rounded-lg shadow-lg w-5/6 max-w-4xl max-h-[80vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Color Palette Code</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            Close
          </Button>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col min-h-0">
          <div className="px-4 pt-2">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="css">CSS Variables</TabsTrigger>
              <TabsTrigger value="json">JSON</TabsTrigger>
              <TabsTrigger value="tailwind">Tailwind Config</TabsTrigger>
            </TabsList>
          </div>
          
          <div className="p-4 flex-1 overflow-auto">
            {Object.entries(formats).map(([key, { code, language }]) => (
              <TabsContent key={key} value={key} className="mt-0 relative h-full">
                <div className="bg-muted rounded-md overflow-auto h-full">
                  <Highlight
                    theme={syntaxTheme}
                    code={code}
                    language={language}
                  >
                    {({ className, style, tokens, getLineProps, getTokenProps }) => (
                      <pre className={className} style={{ ...style, padding: '1rem', borderRadius: '0.375rem' }}>
                        {tokens.map((line, i) => (
                          <div key={i} {...getLineProps({ line })}>
                            {line.map((token, key) => (
                              <span key={key} {...getTokenProps({ token })} />
                            ))}
                          </div>
                        ))}
                      </pre>
                    )}
                  </Highlight>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="absolute top-2 right-2"
                    onClick={() => copyCode(key)}
                  >
                    {copiedFormat === key ? <Check className="h-4 w-4 mr-1" /> : <Copy className="h-4 w-4 mr-1" />}
                    {copiedFormat === key ? "Copied" : "Copy"}
                  </Button>
                </div>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  );
} 