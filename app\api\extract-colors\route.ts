import { NextRequest, NextResponse } from "next/server";
import { ColorResponse } from "@/types";
import { parseColorsFromResponse, generateColorExtractionPrompt } from "@/lib/color-service";

// Maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Allowed image types
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/bmp',
  'image/tiff',
  'image/svg+xml',
  'image/x-icon',
  'image/vnd.microsoft.icon',
  'image/heic',
  'image/heif',
  'image/avif',
  'image/jp2',
  'image/jpx',
  'image/jpm',
  'image/jxl'
];

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const imageFile = formData.get("image") as File;
    
    if (!imageFile) {
      return NextResponse.json(
        { error: "No image file provided" },
        { status: 400 }
      );
    }

    // Validate file size
    if (imageFile.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: "File size exceeds limit" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(imageFile.type)) {
      return NextResponse.json(
        { error: "Invalid file type" },
        { status: 400 }
      );
    }

    // Get existing colors if provided
    let existingColors: string[] = [];
    const existingColorsStr = formData.get("existingColors");
    if (existingColorsStr && typeof existingColorsStr === "string") {
      try {
        existingColors = JSON.parse(existingColorsStr);
        // Validate that it's an array of strings
        if (!Array.isArray(existingColors) || !existingColors.every(color => typeof color === 'string')) {
          throw new Error('Invalid colors format');
        }
      } catch {
        return NextResponse.json(
          { error: "Invalid existing colors format" },
          { status: 400 }
        );
      }
    }

    // Get max colors to return (default to 5 for performance)
    const maxColorsParam = formData.get("maxColors");
    const maxColors = maxColorsParam ? parseInt(maxColorsParam as string, 10) : 5;
    
    // Validate maxColors (allow up to 10 per request to stay within reasonable API limits)
    if (isNaN(maxColors) || maxColors < 1 || maxColors > 10) {
      return NextResponse.json(
        { error: "Invalid max colors value" },
        { status: 400 }
      );
    }

    // Convert the file to base64
    const bytes = await imageFile.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Image = buffer.toString("base64");
    
    // Get API key from environment variables
    const apiKey = process.env.GEMINI_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: "Service temporarily unavailable" },
        { status: 503 }
      );
    }

    // Generate a prompt that includes instruction to find unique colors
    const prompt = generateColorExtractionPrompt(existingColors, maxColors);

    // Make request to Gemini API
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                },
                {
                  inline_data: {
                    mime_type: imageFile.type,
                    data: base64Image
                  }
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.2,
            maxOutputTokens: 1024
          }
        }),
      }
    );

    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to process image" },
        { status: 500 }
      );
    }

    const data = await response.json();
    
    // Parse the response to extract the colors JSON
    const colorsText = data.candidates[0]?.content?.parts[0]?.text || "";
    
    try {
      // Parse the colors from the response
      const colors = await parseColorsFromResponse(colorsText);
      
      const result: ColorResponse = { colors };
      return NextResponse.json(result);
    } catch {
      return NextResponse.json(
        { error: "Failed to process colors" },
        { status: 500 }
      );
    }
  } catch {
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
} 