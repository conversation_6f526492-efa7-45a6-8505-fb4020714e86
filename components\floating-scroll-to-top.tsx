'use client'
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {ArrowUp} from "lucide-react";

export default function FloatingScrollButton() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      setIsVisible(window.scrollY > 300);
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <Button
      onClick={scrollToTop}
      className={`fixed right-4 lg:right-10 bottom-10 z-[9999] rounded-full p-3 shadow-lg transition-all duration-500
        bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
        border-2 border-white/20 backdrop-blur-sm scroll-to-top-btn
        ${
          isVisible
            ? "opacity-100 scale-100"
            : "opacity-0 scale-0 pointer-events-none"
        }`}
      size="icon"
      aria-label="Scroll to top"
    >
      <ArrowUp className="h-5 w-5 text-white" />
    </Button>
  );
}